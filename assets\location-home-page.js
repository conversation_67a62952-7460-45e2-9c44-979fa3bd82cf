/**
 * LocationHomePage class handles home page specific functionality
 * including simplified map display without sidebar and immediate filtering
 */
export class LocationHomePage {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
  }

  /**
   * Initializes home page specific features
   */
  initialize() {
    this.setupSimplifiedFeatures();
    this.setupImmediateFiltering();
  }

  /**
   * Sets up simplified features for home page
   */
  setupSimplifiedFeatures() {
    // Disable sidebar for home page
    this.enableSidebar = false;

    // Enable basic filtering but with immediate response
    this.mapLocation.isEnableRadiusFilter = this.mapLocation.dataset.enableRadiusFilter === 'true';
    this.mapLocation.isEnabledQueryParams = this.mapLocation.dataset.enableUpdateQuearyParams === 'true';

    // Disable delayed filtering for home page (always immediate)
    this.mapLocation.hasApplyButton = false;
    this.mapLocation.shouldUseDelayedMobileFiltering = false;
  }

  /**
   * Sets up immediate filtering for home page
   */
  setupImmediateFiltering() {
    // Home page always uses immediate filtering regardless of device
    this.immediateFiltering = true;
  }

  /**
   * Handles location loading specific to home page
   * @param {Array} locations - Array of locations to load
   */
  loadLocations(locations) {
    // No sidebar for home page, just load markers
    const markers = this.mapLocation.markerClusterer.placeMarkers(locations);
    this.mapLocation.markerClusterer.initMarkerClusterer(markers);

    // For home page, we might want to fit all markers in view
    this.fitMarkersInView(markers);
  }

  /**
   * Fits all markers in the map view
   * @param {Array} markers - Array of markers to fit
   */
  fitMarkersInView(markers) {
    if (markers.length === 0) return;

    const bounds = new google.maps.LatLngBounds();
    markers.forEach((marker) => {
      bounds.extend(marker.position);
    });

    // Only fit bounds if we have multiple markers
    if (markers.length > 1) {
      this.mapLocation.map.fitBounds(bounds, {
        padding: { top: 50, right: 50, bottom: 50, left: 50 },
      });
    }
  }

  /**
   * Handles map change events specific to home page
   * @param {Object} bounds - Map bounds
   * @param {boolean} forceUpdate - Whether to force update
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    // Home page always applies filters immediately
    const filteredLocationsWithinBounds = this.mapLocation.filterLocationsWithinBounds(bounds);

    // Update the selected location coordinates
    this.mapLocation.selectedLocationCoordinates = {
      lat: this.mapLocation.map.getCenter().lat(),
      lng: this.mapLocation.map.getCenter().lng(),
    };

    // Filter locations based on the selected criteria
    const newFilteredLocations = this.mapLocation.filterLocations(
      filteredLocationsWithinBounds,
      this.mapLocation.selectedLocationCoordinates.lat,
      this.mapLocation.selectedLocationCoordinates.lng,
      this.mapLocation.selectedRadius,
      this.mapLocation.selectedBusinessType
    );

    // Only load locations if they have actually changed
    if (this.mapLocation.locationsHaveChanged(newFilteredLocations)) {
      this.mapLocation.previousFilteredLocations = [...newFilteredLocations]; // Store copy for next comparison
      this.mapLocation.filteredLocations = newFilteredLocations;
      this.loadLocations(this.mapLocation.filteredLocations);
    }

    // Update URL parameters if enabled (usually disabled for home page)
    if (this.mapLocation.isEnabledQueryParams) {
      this.mapLocation.updateURLQueryParams(
        this.mapLocation.selectedNearByMe,
        this.mapLocation.selectedInputSearch,
        this.mapLocation.selectedLocationCoordinates.lat,
        this.mapLocation.selectedLocationCoordinates.lng,
        this.mapLocation.selectedRadius,
        this.mapLocation.selectedBusinessType
      );
    }
  }

  /**
   * Handles search functionality for home page
   * @param {string} searchTerm - Search term
   */
  handleSearch(searchTerm) {
    // Use event manager for immediate filtering
    this.mapLocation.eventManager.handleFilterChange('search', searchTerm);
  }

  /**
   * Handles near me functionality for home page
   */
  handleNearMe() {
    // Immediate near me without delay
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.mapLocation.processGeolocation(position);
          this.mapLocation.toggleNearMeButton(true);
        },
        (error) => {
          this.mapLocation.displayGeolocationError(error);
        }
      );
    }
  }

  /**
   * Gets page-specific configuration
   * @returns {Object} Configuration object
   */
  getPageConfig() {
    return {
      enableSidebar: false,
      enableFiltering: true,
      enableDelayedFiltering: false,
      enableQueryParams: this.mapLocation.isEnabledQueryParams,
      enableRadiusFilter: this.mapLocation.isEnableRadiusFilter,
      pageType: 'home-page',
    };
  }

  /**
   * Checks if delayed filtering should be used (always false for home page)
   * @returns {boolean}
   */
  shouldUseDelayedFiltering() {
    return false;
  }

  /**
   * Simplified marker click handling for home page
   * @param {Object} marker - Clicked marker
   * @param {Object} location - Location data
   */
  handleMarkerClick(marker, location) {
    // Deselect all markers first
    this.mapLocation.markerClusterer.deselectAllMarkers();

    // Select the clicked marker
    this.mapLocation.markerClusterer.selectMarker(marker);

    // Open info window
    this.mapLocation.openInfoWindow(marker, location);

    // Center map on marker
    this.mapLocation.map.panTo(marker.position);
  }

  /**
   * Handles page-specific cleanup
   */
  cleanup() {
    // No specific cleanup needed for home page
  }
}
