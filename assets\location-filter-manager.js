/**
 * LocationFilterManager class handles all filtering functionality for the location map
 * including radius filters, business type filters, search filters, near me functionality,
 * URL parameter management, and filter state management
 */
export class LocationFilterManager {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
    this.initializeFilterProperties();
  }

  /**
   * Initializes filter-related properties
   */
  initializeFilterProperties() {
    // Filter state
    this.selectedRadius = this.mapLocation.defaultRadius;
    this.selectedBusinessType = this.mapLocation.defaultBusinessType;
    this.selectedInputSearch = null;
    this.selectedNearByMe = null;
    this.selectedLocationCoordinates = {
      lat: parseFloat(this.mapLocation.dataset.defaultLat),
      lng: parseFloat(this.mapLocation.dataset.defaultLng),
    };
    this.selectedFilterCount = 0;

    // URL parameter keys
    if (this.mapLocation.isEnabledQueryParams) {
      this.nearByMeParams = 'near_by_me';
      this.businessTypeParams = 'business_type';
      this.radiusParams = 'radius';
      this.searchParams = 'search';
      this.latParams = 'lat';
      this.lngParams = 'lng';
    }
  }

  /**
   * Binds all filter-related events
   */
  bindFilterEvents() {
    this.bindRadiusFilterEvent();
    this.bindBusinessTypeFilterEvent();
    this.bindApplyAllFiltersEvent();
    this.resetAllFilters();
    this.handleClose();
  }

  /**
   * Binds the event for the "radius search option".
   * Adds an event listener that triggers the handleRadiusFilterClick method when the radius filter changes.
   */
  bindRadiusFilterEvent() {
    const radiusOptions = this.mapLocation.querySelectorAll('.select-radius');
    if (!radiusOptions.length) return;

    radiusOptions.forEach((optionItem) =>
      optionItem.addEventListener('click', () => this.handleRadiusSelection(optionItem))
    );
  }

  /**
   * Handles the selection of a new radius from the dropdown.
   * - Updates the selected radius value.
   * - If query parameters are enabled, it updates the URL to reflect the new radius.
   * - On desktop: Adjusts the map zoom level immediately.
   * - On mobile: Stores the selection for later application via "Apply Now" button.
   *
   * @param {HTMLElement} optionItem - The selected radius dropdown option.
   */
  handleRadiusSelection(optionItem) {
    this.selectedRadius = parseInt(optionItem.dataset.value);

    // Update the UI to reflect the selected radius
    this.updateRadiusUI();

    // Update filter count
    this.updateFilterCount();

    // Apply filters immediately on desktop or home page
    if (!this.shouldUseDelayedFiltering()) {
      if (!this.mapLocation.isMobile) this.mapLocation.mapLoader.showLoader();
      
      if (this.mapLocation.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType
        );
      }

      if (!this.mapLocation.isMobile) {
        setTimeout(() => this.mapLocation.mapLoader.hideLoader(), 500);
      }
    }
  }

  /**
   * Updates the radius UI elements
   */
  updateRadiusUI() {
    const radiusElement = document.getElementById('radiusSelectorSelectedLabel');
    if (this.selectedRadius > 0) {
      radiusElement.innerText = `${this.selectedRadius} miles`;
      this.mapLocation.highlightSelectedOption(this.selectedRadius);
      radiusElement.classList.replace('text-gray-5', 'text-gray-8');
    } else {
      radiusElement.innerText = this.mapLocation.locationString.searchRadius;
      radiusElement.classList.replace('text-gray-8', 'text-gray-5');
    }
  }

  /**
   * Binds the event for the business type filter dropdown.
   */
  bindBusinessTypeFilterEvent() {
    const businessTypeOptions = this.mapLocation.querySelectorAll('.business-type-item');
    if (!businessTypeOptions.length) return;

    businessTypeOptions.forEach((optionItem) =>
      optionItem.addEventListener('click', () => this.handleBusinessTypeFilterSelection(optionItem))
    );
  }

  /**
   * Handles the selection of a business type filter from the dropdown.
   * - On desktop: Shows loader, updates URL params, and applies changes immediately.
   * - On mobile: Stores the selection for later application via "Apply Now" button.
   *
   * @param {HTMLElement} optionItem - The selected business type dropdown option.
   */
  handleBusinessTypeFilterSelection(optionItem) {
    if (!this.mapLocation.isMobile) this.mapLocation.mapLoader.showLoader();

    this.selectedBusinessType = optionItem.dataset.value;
    this.selectedBusinessType =
      this.selectedBusinessType === 'all_facilities' ? this.mapLocation.defaultBusinessType : this.selectedBusinessType;

    // Update filter count
    this.updateFilterCount();

    // Apply filters immediately on desktop or home page
    if (!this.shouldUseDelayedFiltering()) {
      if (this.mapLocation.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType
        );
      }

      if (!this.mapLocation.isMobile) {
        setTimeout(() => this.mapLocation.mapLoader.hideLoader(), 500);
      }
    }
  }

  /**
   * Binds the event for the "Apply All Filters" button.
   * This button is typically used on mobile devices to apply all selected filters at once.
   */
  bindApplyAllFiltersEvent() {
    const applyFiltersButton = document.getElementById('applyAllFilters');
    if (!applyFiltersButton) return;

    applyFiltersButton.addEventListener('click', () => {
      const dismissModal = document.querySelector('[data-dismiss="modal-selector"]');

      // Show loader while applying filters
      if (this.mapLocation.isMobile) this.mapLocation.mapLoader.showLoader();

      // Update URL search query parameters
      if (this.mapLocation.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType,
          true
        );
      }

      // Apply map zoom changes for mobile (delayed from filter selection, Near Me, or search)
      if (this.mapLocation.isMobile) {
        // Handle Near Me location centering
        if (this.selectedNearByMe && this.selectedLocationCoordinates) {
          this.mapLocation.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.mapLocation.markerCenterMapZoomLevel);
        }
        // Handle search input location centering
        else if (this.selectedInputSearch && this.selectedLocationCoordinates) {
          const zoomLevel = this.mapLocation.isCenterChangeRestricted ? 12 : this.mapLocation.zoomLevel;
          this.mapLocation.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, zoomLevel);
        }
        // Handle radius-based centering
        else if (this.selectedRadius === this.mapLocation.defaultRadius) {
          this.mapLocation.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.mapLocation.zoomLevel);
        } else if (this.mapLocation.map.getZoom() < this.mapLocation.markerCenterMapZoomLevel) {
          this.mapLocation.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.mapLocation.zoomLevel + 2);
        }
      }

      // Apply filters to map (force update on mobile)
      if (this.mapLocation.mapBound) this.mapLocation.eventManager.forceUpdate();

      this.updateFilterCount();

      // Hide loader after a short delay
      if (this.mapLocation.isMobile) setTimeout(() => this.mapLocation.mapLoader.hideLoader(), 500);

      if (dismissModal) {
        dismissModal.click();
      }
    });
  }

  /**
   * Updates the filter count display
   * @param {number} numParams - Number of active filters
   */
  updateFilterCount(numParams = null) {
    if (numParams === null) {
      numParams = this.getActiveFilterCount();
    }

    this.selectedFilterCount = numParams;

    const activeFilterCountButton = document.querySelector('.active-filter-count');
    if (activeFilterCountButton) {
      const filterCount = activeFilterCountButton.querySelector('.filter-count');
      filterCount.textContent = numParams;
      activeFilterCountButton.style.display = numParams > 0 ? 'inline-block' : 'none';
    }
  }

  /**
   * Gets the count of currently active filters
   * @returns {number} Number of active filters
   */
  getActiveFilterCount() {
    let count = 0;
    
    if (this.selectedNearByMe) count++;
    if (this.selectedInputSearch) count++;
    if (this.selectedRadius > 0) count++;
    if (this.selectedBusinessType && this.selectedBusinessType !== this.mapLocation.defaultBusinessType) count++;
    
    return count;
  }

  /**
   * Checks if delayed filtering should be used
   * @returns {boolean}
   */
  shouldUseDelayedFiltering() {
    return this.mapLocation.pageLogic.shouldUseDelayedFiltering();
  }

  /**
   * Checks if there are any active filters applied.
   * Returns true if any filters are active, false if all are at default values.
   */
  hasActiveFilters() {
    return (
      this.selectedNearByMe !== null ||
      this.selectedInputSearch !== null ||
      this.selectedRadius !== this.mapLocation.defaultRadius ||
      this.selectedBusinessType !== this.mapLocation.defaultBusinessType
    );
  }

  /**
   * Resets all filters to their default values
   */
  resetAllFilters() {
    const resetFiltersButton = document.getElementById('resetAllFilters');
    if (!resetFiltersButton) return;

    resetFiltersButton.addEventListener('click', () => {
      // Reset all filter values
      this.selectedRadius = this.mapLocation.defaultRadius;
      this.selectedBusinessType = this.mapLocation.defaultBusinessType;
      this.selectedInputSearch = null;
      this.selectedNearByMe = null;
      
      // Reset location coordinates to default
      this.selectedLocationCoordinates = {
        lat: parseFloat(this.mapLocation.dataset.defaultLat),
        lng: parseFloat(this.mapLocation.dataset.defaultLng),
      };

      // Update UI elements
      this.updateRadiusUI();
      this.updateFilterCount(0);

      // Clear URL parameters
      if (this.mapLocation.isEnabledQueryParams) {
        this.clearURLQueryParams();
      }

      // Reset map view
      this.mapLocation.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.mapLocation.zoomLevel);
      
      // Apply reset filters
      if (this.mapLocation.mapBound) this.mapLocation.eventManager.forceUpdate();
    });
  }

  /**
   * Handles the close functionality for filter modals
   */
  handleClose() {
    const closeButtons = document.querySelectorAll('[data-dismiss="modal-selector"]');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Any cleanup needed when closing filter modals
      });
    });
  }

  /**
   * Updates the URL query parameters based on the provided filter values.
   *
   * This function modifies the browser's URL without reloading the page, ensuring that
   * search and filter parameters are reflected in the URL. If a parameter is provided,
   * it is added or updated in the query string; if it is null or undefined, it is removed.
   *
   * @param {boolean} nearByMe - Indicates if the "Near Me" filter is applied.
   * @param {string} search - The search term entered by the user.
   * @param {number} lat - latitude value for location-based filtering.
   * @param {number} lng - longitude value for location-based filtering.
   * @param {number} radius - Search radius for filtering results.
   * @param {string} businessType - The type of business being searched for.
   * @param {boolean} IsApplyFilters - Whether filters should be applied on mobile devices.
   */
  updateURLQueryParams(nearByMe, search, lat, lng, radius, businessType, IsApplyFilters) {
    if (this.mapLocation.isMobile && !IsApplyFilters) return;

    const params = new URLSearchParams(window.location.search);

    if (nearByMe != null) {
      params.set(this.nearByMeParams, nearByMe);
    } else {
      params.delete(this.nearByMeParams);
    }

    if (search) {
      params.set(this.searchParams, search);
    } else {
      params.delete(this.searchParams);
    }

    if (lat) {
      params.set(this.latParams, lat);
    } else {
      params.delete(this.latParams);
    }

    if (lng) {
      params.set(this.lngParams, lng);
    } else {
      params.delete(this.lngParams);
    }

    if (radius && radius > 0) {
      params.set(this.radiusParams, radius);
    } else {
      params.delete(this.radiusParams);
    }

    if (businessType) {
      params.set(this.businessTypeParams, businessType);
    } else {
      params.delete(this.businessTypeParams);
    }

    // Update the URL without reloading the page
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  }

  /**
   * Clears all URL query parameters
   */
  clearURLQueryParams() {
    const newUrl = window.location.pathname;
    window.history.replaceState({}, '', newUrl);
  }

  /**
   * Parses URL search parameters and updates the filter state accordingly.
   *
   * This method extracts query parameters from the URL and updates the filter values
   * such as location, search input, radius, and business type. It also ensures
   * that filters are applied correctly and updates the UI.
   */
  parseSearchParams() {
    const params = new URLSearchParams(window.location.search);

    this.selectedNearByMe = params.get(this.nearByMeParams) === 'true' || null;
    this.selectedInputSearch = params.get(this.searchParams);

    const lat = params.get(this.latParams);
    const lng = params.get(this.lngParams);

    if (lat && lng) {
      this.selectedLocationCoordinates = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
      };
    }

    const radius = params.get(this.radiusParams);
    this.selectedRadius = radius ? parseInt(radius) : this.mapLocation.defaultRadius;

    this.selectedBusinessType = params.get(this.businessTypeParams);

    if (this.mapLocation.isEnabledQueryParams) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType,
        true
      );
    }

    // Update filters count
    this.updateFilterCount();
  }

  /**
   * Filters a list of locations based on their distance from a given point and optional business type.
   *
   * @param {Array} locations - An array of location objects. Each object should have `latitude` and `longitude` properties.
   * @param {number} lat - The latitude of the reference point.
   * @param {number} lng - The longitude of the reference point.
   * @param {number} radius - The search radius in miles.
   * @param {string} businessType - The type of business to filter by.
   * @returns {Array} Filtered array of locations.
   */
  filterLocations(locations, lat, lng, radius, businessType) {
    radius = radius ? radius * 1609.34 : Infinity;

    let filteredLocations = locations.filter((location) => {
      try {
        const distance = this.mapLocation.calculateDistance(
          lat,
          lng,
          parseFloat(location.latitude),
          parseFloat(location.longitude)
        );

        return distance <= radius;
      } catch (error) {
        return false;
      }
    });

    if (businessType) {
      filteredLocations = filteredLocations.filter(
        (location) => location.businessTypeName?.toLowerCase() === businessType.toLowerCase()
      );
    }

    return filteredLocations;
  }

  /**
   * Filters locations within the current map bounds
   * @param {google.maps.LatLngBounds} bounds - Map bounds
   * @returns {Array} Filtered locations within bounds
   */
  filterLocationsWithinBounds(bounds) {
    if (!bounds) return [];

    const filtered = this.mapLocation.allLocations.filter((location) => {
      const locationLatLng = new google.maps.LatLng(parseFloat(location.latitude), parseFloat(location.longitude));
      return bounds.contains(locationLatLng);
    });

    return filtered;
  }

  /**
   * Handles search input functionality
   * @param {string} searchTerm - The search term
   */
  handleInputSearch(searchTerm) {
    this.selectedInputSearch = searchTerm;

    // Apply search immediately if not using delayed filtering
    if (!this.shouldUseDelayedFiltering()) {
      this.mapLocation.eventManager.handleFilterChange('search', searchTerm);
    }
  }

  /**
   * Handles near me functionality
   * @param {boolean} isActive - Whether near me is active
   */
  handleNearMe(isActive) {
    this.selectedNearByMe = isActive;

    // Apply filter immediately if not using delayed filtering
    if (!this.shouldUseDelayedFiltering()) {
      this.mapLocation.eventManager.handleFilterChange('nearMe', isActive);
    }
  }

  /**
   * Gets current filter state
   * @returns {Object} Current filter values
   */
  getFilterState() {
    return {
      radius: this.selectedRadius,
      businessType: this.selectedBusinessType,
      search: this.selectedInputSearch,
      nearMe: this.selectedNearByMe,
      coordinates: this.selectedLocationCoordinates,
      filterCount: this.selectedFilterCount
    };
  }

  /**
   * Sets filter state
   * @param {Object} filterState - Filter state to set
   */
  setFilterState(filterState) {
    if (filterState.radius !== undefined) this.selectedRadius = filterState.radius;
    if (filterState.businessType !== undefined) this.selectedBusinessType = filterState.businessType;
    if (filterState.search !== undefined) this.selectedInputSearch = filterState.search;
    if (filterState.nearMe !== undefined) this.selectedNearByMe = filterState.nearMe;
    if (filterState.coordinates !== undefined) this.selectedLocationCoordinates = filterState.coordinates;

    this.updateFilterCount();
  }
}
