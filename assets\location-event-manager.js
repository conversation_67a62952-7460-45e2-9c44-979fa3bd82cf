/**
 * LocationEventManager class handles all map events with proper throttling and debouncing
 * to prevent excessive calls and improve performance
 */
export class LocationEventManager {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
    this.isProcessing = false;
    this.pendingUpdate = null;
    this.lastBounds = null;
    this.lastUpdateTime = 0;
    this.minUpdateInterval = 300; // Minimum time between updates (ms)
    
    // Bind methods to preserve context
    this.handleMapChange = this.handleMapChange.bind(this);
    this.processMapUpdate = this.processMapUpdate.bind(this);
  }

  /**
   * Initializes event listeners for the map
   */
  initialize() {
    if (this.mapLocation.map) {
      // Use a single idle listener with proper throttling
      this.idleListener = this.mapLocation.map.addListener('idle', this.handleMapChange);
    }
  }

  /**
   * Main map change handler with intelligent throttling
   * @param {boolean} forceUpdate - Force immediate update
   */
  handleMapChange(forceUpdate = false) {
    const now = Date.now();
    const bounds = this.mapLocation.map?.getBounds();
    
    if (!bounds) return;

    // Check if we should skip this update
    if (!forceUpdate && this.shouldSkipUpdate(bounds, now)) {
      this.scheduleDelayedUpdate(bounds);
      return;
    }

    this.processMapUpdate(bounds, forceUpdate);
  }

  /**
   * Determines if we should skip this update based on timing and bounds
   * @param {google.maps.LatLngBounds} bounds - Current map bounds
   * @param {number} now - Current timestamp
   * @returns {boolean}
   */
  shouldSkipUpdate(bounds, now) {
    // Skip if already processing
    if (this.isProcessing) return true;

    // Skip if too soon since last update
    if (now - this.lastUpdateTime < this.minUpdateInterval) return true;

    // Skip if bounds haven't changed significantly
    if (this.lastBounds && this.boundsAreEqual(bounds, this.lastBounds)) return true;

    return false;
  }

  /**
   * Schedules a delayed update if one isn't already pending
   * @param {google.maps.LatLngBounds} bounds - Map bounds to update with
   */
  scheduleDelayedUpdate(bounds) {
    if (this.pendingUpdate) {
      clearTimeout(this.pendingUpdate);
    }

    this.pendingUpdate = setTimeout(() => {
      this.pendingUpdate = null;
      this.processMapUpdate(bounds, false);
    }, this.minUpdateInterval);
  }

  /**
   * Processes the actual map update
   * @param {google.maps.LatLngBounds} bounds - Map bounds
   * @param {boolean} forceUpdate - Whether to force update
   */
  async processMapUpdate(bounds, forceUpdate = false) {
    if (this.isProcessing && !forceUpdate) return;

    this.isProcessing = true;
    this.lastUpdateTime = Date.now();
    this.lastBounds = bounds;
    this.mapLocation.mapBound = bounds;

    try {
      // Delegate to page-specific logic
      await this.mapLocation.pageLogic.handleMapChangeEvent(bounds, forceUpdate);
    } catch (error) {
      console.error('Error processing map update:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Forces an immediate map update
   * @param {google.maps.LatLngBounds} bounds - Optional bounds, uses current if not provided
   */
  forceUpdate(bounds = null) {
    const mapBounds = bounds || this.mapLocation.map?.getBounds();
    if (mapBounds) {
      this.processMapUpdate(mapBounds, true);
    }
  }

  /**
   * Checks if two bounds are approximately equal
   * @param {google.maps.LatLngBounds} bounds1 - First bounds
   * @param {google.maps.LatLngBounds} bounds2 - Second bounds
   * @returns {boolean}
   */
  boundsAreEqual(bounds1, bounds2) {
    if (!bounds1 || !bounds2) return false;

    const tolerance = 0.001; // Degrees tolerance
    const ne1 = bounds1.getNorthEast();
    const sw1 = bounds1.getSouthWest();
    const ne2 = bounds2.getNorthEast();
    const sw2 = bounds2.getSouthWest();

    return (
      Math.abs(ne1.lat() - ne2.lat()) < tolerance &&
      Math.abs(ne1.lng() - ne2.lng()) < tolerance &&
      Math.abs(sw1.lat() - sw2.lat()) < tolerance &&
      Math.abs(sw1.lng() - sw2.lng()) < tolerance
    );
  }

  /**
   * Handles filter changes with appropriate throttling
   * @param {string} filterType - Type of filter changed
   * @param {*} value - New filter value
   */
  handleFilterChange(filterType, value) {
    // Update the filter value
    switch (filterType) {
      case 'radius':
        this.mapLocation.selectedRadius = value;
        break;
      case 'businessType':
        this.mapLocation.selectedBusinessType = value;
        break;
      case 'search':
        this.mapLocation.selectedInputSearch = value;
        break;
      case 'nearMe':
        this.mapLocation.selectedNearByMe = value;
        break;
    }

    // Apply filters based on page logic
    if (!this.mapLocation.pageLogic.shouldUseDelayedFiltering()) {
      this.forceUpdate();
    }
  }

  /**
   * Handles geolocation updates
   * @param {Object} position - Geolocation position
   */
  handleGeolocationUpdate(position) {
    const { latitude, longitude } = position.coords;
    
    this.mapLocation.selectedLocationCoordinates = { lat: latitude, lng: longitude };
    this.mapLocation.locakedCenterPoint = { lat: latitude, lng: longitude };
    
    // Center map and force update
    this.mapLocation.centerMapUpdateZoomLevel(
      this.mapLocation.selectedLocationCoordinates, 
      this.mapLocation.markerCenterMapZoomLevel
    );
    
    // Force update after map centers
    setTimeout(() => this.forceUpdate(), 500);
  }

  /**
   * Cleanup method to remove listeners and clear timeouts
   */
  cleanup() {
    if (this.idleListener) {
      google.maps.event.removeListener(this.idleListener);
      this.idleListener = null;
    }

    if (this.pendingUpdate) {
      clearTimeout(this.pendingUpdate);
      this.pendingUpdate = null;
    }

    this.isProcessing = false;
    this.lastBounds = null;
  }

  /**
   * Gets current processing status
   * @returns {boolean}
   */
  isCurrentlyProcessing() {
    return this.isProcessing;
  }

  /**
   * Resets the event manager state
   */
  reset() {
    this.isProcessing = false;
    this.lastBounds = null;
    this.lastUpdateTime = 0;
    
    if (this.pendingUpdate) {
      clearTimeout(this.pendingUpdate);
      this.pendingUpdate = null;
    }
  }
}
