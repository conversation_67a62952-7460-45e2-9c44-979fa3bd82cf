/**
 * LocationMarkerClusterer class handles all marker clustering functionality
 * including marker creation, clustering configuration, and cluster rendering
 */
export class LocationMarkerClusterer {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
    this.markerClusterer = null;
  }

  /**
   * Displays the provided location markers on the map.
   * Optimized to only update changed markers instead of clearing all markers.
   * @param {Array} locations - Array of location objects to be displayed as markers on the map.
   */
  placeMarkers(locations) {
    // Skip update if markers are already correct
    if (!this.markersNeedUpdate(locations)) {
      return this.mapLocation.mapMarkers;
    }

    // Get current map bounds to optimize marker visibility
    const bounds = this.mapLocation.map ? this.mapLocation.map.getBounds() : null;

    // Create a set of location IDs for quick lookup
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // Remove markers that are no longer needed or outside bounds
    this.mapLocation.mapMarkers = this.mapLocation.mapMarkers.filter((marker) => {
      const shouldKeep = marker.locationId && newLocationIds.has(marker.locationId);

      // Also check if marker is within bounds (optional optimization)
      const withinBounds = !bounds || bounds.contains(marker.position);

      if (!shouldKeep || !withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });

    // Get existing marker location IDs
    const existingLocationIds = new Set(this.mapLocation.mapMarkers.map((marker) => marker.locationId));

    // Add new markers for locations that don't already have markers
    locations.forEach((location) => {
      if (!existingLocationIds.has(location.userId)) {
        const marker = this.createMarker(this.mapLocation.map, location);
        marker.locationId = location.userId; // Store location ID for tracking
        this.mapLocation.mapMarkers.push(marker);

        // Add a click listener to handle marker interactions
        marker.addListener('gmp-click', () => {
          this.mapLocation.handleMarkerClick(marker, location);
        });
      }
    });

    return this.mapLocation.mapMarkers;
  }

  /**
   * Creates a new Google Maps marker for the given location.
   *
   * @param {Object} location - The location data for the marker.
   * @returns {google.maps.Marker} - The created marker instance.
   */
  createMarker(map, location) {
    const mapMarker = document.createElement('div');
    const img = document.createElement('img');
    img.setAttribute('loading', 'lazy');
    img.setAttribute('alt', location.companyName);
    img.setAttribute('aria-label', location.companyName);
    img.setAttribute('class', 'marker-icon');
    img.src = location.isHealthPassPartner
      ? 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/marker-primary-gradient.svg'
      : 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/secondary-marker.svg';
    mapMarker.appendChild(img);

    const marker = new google.maps.marker.AdvancedMarkerElement({
      position: { lat: parseFloat(location.latitude), lng: parseFloat(location.longitude) },
      map: map,
      zIndex: location.isHealthPassPartner ? 1 : 0,
      title: location.companyName,
      content: mapMarker,
    });

    // Add isSelected property to track selected state
    marker.isSelected = false;

    return marker;
  }

  /**
   * Checks if markers need to be updated based on location changes
   * @param {Array} locations - Current locations to display
   * @returns {boolean} - True if markers need updating
   */
  markersNeedUpdate(locations) {
    // If no existing markers, definitely need update
    if (this.mapLocation.mapMarkers.length === 0) {
      return true;
    }

    // If location count changed, need update
    if (this.mapLocation.mapMarkers.length !== locations.length) {
      return true;
    }

    // Check if location IDs match
    const currentLocationIds = new Set(this.mapLocation.mapMarkers.map((marker) => marker.locationId));
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // If sets are different sizes or have different elements, need update
    if (currentLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (const id of newLocationIds) {
      if (!currentLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Initializes or updates a MarkerClusterer with custom rendering and clustering logic.
   * @param {Array} markers - Array of markers to cluster
   */
  initMarkerClusterer(markers) {
    // If clusterer already exists, update it with new markers instead of recreating
    if (this.markerClusterer) {
      this.markerClusterer.clearMarkers();
      this.markerClusterer.addMarkers(markers);
    } else {
      // Create new clusterer
      this.markerClusterer = new markerClusterer.MarkerClusterer({
        markers: markers,
        map: this.mapLocation.map,
        algorithm: new markerClusterer.GridAlgorithm({ gridSize: 38, maxZoom: 13 }),
        renderer: {
          render: ({ count, position, markers, map }) => {
            const clusterElement = document.createElement('div');
            clusterElement.className = 'custom-cluster';
            clusterElement.innerHTML = `<div class="cluster-content"><span class="cluster-count">${count}</span></div>`;
            clusterElement.addEventListener('click', () => {
              const bounds = new google.maps.LatLngBounds();
              markers.forEach((marker) => bounds.extend(marker.position));
              map.fitBounds(bounds, { padding: { top: 18, right: 18, bottom: 18, left: 18 } });
            });
            return new google.maps.marker.AdvancedMarkerElement({
              position,
              content: clusterElement,
            });
          },
        },
      });
    }
  }

  /**
   * Attempts to re-select a marker and open its info window after refreshing the map.
   * @param {Object} selectedLocation - Previously selected location
   * @param {Object} selectedMarkerPosition - Previously selected marker position
   * @param {Array} locations - Current locations array
   */
  restoreSelectedMarker(selectedLocation, selectedMarkerPosition, locations) {
    const markerToSelect = this.findMarkerToSelect(selectedLocation, selectedMarkerPosition);

    if (!markerToSelect) return;

    const locationToSelect = selectedLocation || this.findLocationByCoords(locations, markerToSelect.position);

    if (locationToSelect) {
      this.mapLocation.openInfoWindow(markerToSelect, locationToSelect, { disableAutoPan: true });
    }
  }

  /**
   * Finds a marker that matches either the selected location or previously saved coordinates.
   * @param {Object} selectedLocation - Previously selected location
   * @param {Object} markerPosition - Previously selected marker position
   * @returns {Object|null} - Found marker or null
   */
  findMarkerToSelect(selectedLocation, markerPosition) {
    if (selectedLocation) {
      return this.mapLocation.mapMarkers.find(
        (marker) =>
          marker.position.lat === parseFloat(selectedLocation.latitude) &&
          marker.position.lng === parseFloat(selectedLocation.longitude)
      );
    }

    if (markerPosition) {
      return this.mapLocation.mapMarkers.find(
        (marker) => marker.position.lat === markerPosition.lat && marker.position.lng === markerPosition.lng
      );
    }

    return null;
  }

  /**
   * Finds the location object from a list that matches the given lat/lng coordinates.
   * @param {Array} locations - Array of location objects
   * @param {Object} position - Position object with lat/lng
   * @returns {Object|null} - Found location or null
   */
  findLocationByCoords(locations, position) {
    return locations.find(
      (loc) => parseFloat(loc.latitude) === position.lat && parseFloat(loc.longitude) === position.lng
    );
  }

  /**
   * Deselects all markers and resets their visual state
   */
  deselectAllMarkers() {
    this.mapLocation.mapMarkers.forEach((marker) => {
      marker.isSelected = false;
      marker.zIndex = marker.locationId && this.mapLocation.allLocations.find(loc => loc.userId === marker.locationId)?.isHealthPassPartner ? 1 : 0;
      marker.content?.classList.remove('selected-marker');
    });
  }

  /**
   * Selects a specific marker and updates its visual state
   * @param {Object} marker - The marker to select
   */
  selectMarker(marker) {
    // First deselect all markers
    this.deselectAllMarkers();

    // Then select the specified marker
    marker.isSelected = true;
    marker.zIndex = 2; // Show the selected marker at top
    marker.content?.classList.add('selected-marker');
  }
}
