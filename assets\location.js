import { GoogleMapsLoader } from '@theme/locations-services';
import { LocationApiService } from '@theme/locations-services';
import { MapLoader } from '@theme/locations-services';
import { LocationSidebar } from '@theme/location-sidebar';
import { LocationMarkerClusterer } from '@theme/location-marker-clusterer';
import { LocationMainPage } from '@theme/location-main-page';
import { LocationHomePage } from '@theme/location-home-page';
import { LocationEventManager } from '@theme/location-event-manager';
import { LocationFilterManager } from '@theme/location-filter-manager';

class MapLocation extends HTMLElement {
  constructor() {
    super();
    this.initializeProperties();
    this.mapLoader = new MapLoader(this.querySelector('.map-box-section'));
    this.locationSidebar = new LocationSidebar(this);
    this.markerClusterer = new LocationMarkerClusterer(this);
    this.eventManager = new LocationEventManager(this);
    this.filterManager = new LocationFilterManager(this);

    // Initialize page-specific logic based on template
    this.initializePageLogic();
  }

  /**
   * Initializes page-specific logic based on current template
   */
  initializePageLogic() {
    // Determine page type based on template or data attributes
    const template = document.body.dataset.template || '';
    const isMainLocationPage = template.includes('page.locations') || this.dataset.sidebarLocationList === 'true';

    if (isMainLocationPage) {
      this.pageLogic = new LocationMainPage(this);
    } else {
      this.pageLogic = new LocationHomePage(this);
    }

    this.pageLogic.initialize();
  }

  initializeProperties() {
    this.specialOfferingHealthPass = ['Preventative Body Scan', 'Healthpass', 'Health Screen'];
    this.isMobile = window.matchMedia('(max-width: 768px)').matches;
    this.geolocationErrorMessage = window.utilsString.locationsString.errorMessage;
    this.locationString = window.utilsString.locationsString;

    // Convert dataset value to a proper boolean
    this.isEnableRadiusFilter = this.dataset.enableRadiusFilter === 'true';
    this.isEnabledQueryParams = this.dataset.enableUpdateQuearyParams === 'true';

    // Check if "Apply Now" button exists to determine if we should use delayed mobile filtering
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;

    // Default values for locations
    this.allLocations = [];
    this.mapMarkers = [];
    this.filteredLocations = [];
    this.markerClusterer = null;
    this.mapBound = null;
    this.infoWindow;
    this.map;
    this.defaultRadius = this.isEnableRadiusFilter ? Number(this.dataset.defaultRadius) : Infinity;
    this.defaultBusinessType = null;

    /**
     * Set default location coordinates using provided dataset values
     * Fallback to Los Angeles (lat: 34.052235, lng: -118.243683) if dataset values are undefined or invalid.
     */
    this.defaultLocationCoordinates = {
      lat: Number(this.dataset.defaultLat) || 34.052235,
      lng: Number(this.dataset.defaultLng) || -118.243683,
    };

    this.isCenterChangeRestricted = false;
    this.locakedCenterPoint = null;

    // Selected location coordinates (will be managed by filter manager)
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.googleMapId = this.dataset.googleMapId;

    this.zoomLevel = this.isMobile ? Number(this.dataset.defaultZoomMobile) : Number(this.dataset.defaultZoom) || 10;
    this.maxZoomLevel = this.isMobile
      ? Number(this.dataset.defaultMaxZoomMobile)
      : Number(this.dataset.defaultMaxZoom) || 10;

    this.markerCenterMapZoomLevel = this.isMobile
      ? Number(this.dataset.markerCenterMapZoomLevel)
      : Number(this.dataset.markerCenterMapZoomLevelMobile) || 10;
  }

  // Delegate filter properties to filter manager
  get selectedRadius() {
    return this.filterManager?.selectedRadius ?? this.defaultRadius;
  }
  set selectedRadius(value) {
    if (this.filterManager) this.filterManager.selectedRadius = value;
  }

  get selectedBusinessType() {
    return this.filterManager?.selectedBusinessType ?? this.defaultBusinessType;
  }
  set selectedBusinessType(value) {
    if (this.filterManager) this.filterManager.selectedBusinessType = value;
  }

  get selectedInputSearch() {
    return this.filterManager?.selectedInputSearch ?? null;
  }
  set selectedInputSearch(value) {
    if (this.filterManager) this.filterManager.selectedInputSearch = value;
  }

  get selectedNearByMe() {
    return this.filterManager?.selectedNearByMe ?? null;
  }
  set selectedNearByMe(value) {
    if (this.filterManager) this.filterManager.selectedNearByMe = value;
  }

  get selectedFilterCount() {
    return this.filterManager?.selectedFilterCount ?? 0;
  }
  set selectedFilterCount(value) {
    if (this.filterManager) this.filterManager.selectedFilterCount = value;
  }

  // Delegate filter methods to filter manager
  hasActiveFilters() {
    return this.filterManager?.hasActiveFilters() ?? false;
  }
  shouldUseDelayedFiltering() {
    return this.pageLogic?.shouldUseDelayedFiltering() ?? false;
  }
  updateFilterCount(count) {
    if (this.filterManager) this.filterManager.updateFilterCount(count);
  }
  filterLocations(locations, lat, lng, radius, businessType) {
    return this.filterManager?.filterLocations(locations, lat, lng, radius, businessType) ?? locations;
  }
  filterLocationsWithinBounds(bounds) {
    return this.filterManager?.filterLocationsWithinBounds(bounds) ?? [];
  }

  // Delegate search methods to filter manager
  handleInputSearch(query) {
    if (this.filterManager) this.filterManager.handleInputSearch(query);
  }
  handleNearMeClick() {
    if (this.filterManager) this.filterManager.handleNearMeClick();
  }
  processGeolocation(position) {
    if (this.filterManager) this.filterManager.processGeolocation(position);
  }
  toggleNearMeButton(isSelected) {
    if (this.filterManager) this.filterManager.toggleNearMeButton(isSelected);
  }
  hideAutocompleteSuggestions() {
    if (this.filterManager) this.filterManager.hideAutocompleteSuggestions();
  }

  setFilterLoading(isLoading) {
    const filterWrapper = this.querySelector('.filter-wrapper');

    if (filterWrapper) {
      filterWrapper.dataset.loading = isLoading.toString();
    }
  }

  /**
   * Lifecycle method that runs when the component is added to the DOM.
   * - Initializes the map location.
   * - Binds UI events for user interactions.
   * - Parses URL query parameters if enabled.
   */
  async connectedCallback() {
    this.setFilterLoading(true); // Set loading state before initialization

    // Detect filtering behavior early, before map initialization
    this.detectFilteringBehavior();

    await this.initializeMapLocation();
    this.bindUIEvents();

    if (this.isEnabledQueryParams) {
      this.filterManager.parseSearchParams();
    }

    // Listen for custom 'filterDropdown:change' event and bind the handler to current context
    document.addEventListener('filterDropdown:change', this.onFilterDropdownChange.bind(this));
  }

  /**
   * Cleanup method to remove event listeners and prevent memory leaks
   */
  disconnectedCallback() {
    // Cleanup event manager
    if (this.eventManager) {
      this.eventManager.cleanup();
    }

    // Cleanup page logic
    if (this.pageLogic) {
      this.pageLogic.cleanup();
    }
  }

  /**
   * Event handler for dropdown filter changes.
   * Responds to 'radiusSelector' and 'businessTypeSelector' changes.
   * Updates the selected filter values and triggers relevant map update logic.
   * On mobile, filters are stored but not applied until "Apply Now" is clicked.
   *
   * @param {CustomEvent} e - Custom event containing dropdown ID and selected value.
   */
  onFilterDropdownChange(e) {
    const { id, value } = e.detail;
    const highlightSelectedOption = this.highlightSelectedOption(value);

    switch (id) {
      case 'radiusSelector':
        this.eventManager.handleFilterChange('radius', value);
        this.filterManager.updateRadiusUI();
        break;
      case 'businessTypeSelector':
        this.eventManager.handleFilterChange('businessType', value);
        this.filterManager.updateFilterCount();
        break;
      default:
        break;
    }
  }

  /**
   * Initializes the map location by loading the Google Maps API,
   * fetching location data, and rendering the map.
   */
  async initializeMapLocation() {
    const { googleApiKey, locationsApi } = this.dataset;

    if (!googleApiKey || !locationsApi) {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
      return;
    }

    try {
      this.mapLoader.showLoader(); // Show loader while the map is loading
      await this.checkGeolocationPermission(); // Check for geolocation permission on page load

      // Load Google Maps API with the required libraries
      await GoogleMapsLoader.loadGoogleMapsAPI({
        key: googleApiKey,
        libraries: 'maps,geometry,marker',
        markerClusterer: true,
      });

      // Fetch location data from the API
      const locations = await LocationApiService.fetchLocations(locationsApi);
      if (!locations) {
        throw new Error('Unable to fetch locations data');
      }

      const allowedCountries =
        this.dataset.filterCountries?.split(',').map((country) => country.trim().toLowerCase()) ?? [];

      this.allLocations = Array.isArray(locations)
        ? locations.filter((location) => {
            const isInAllowedCountry =
              allowedCountries.length === 0 || allowedCountries.includes(location.country?.trim()?.toLowerCase());
            return isInAllowedCountry;
          })
        : [];

      // Initialize the map inside the specified container
      await this.initializeMap(this.querySelector('.map-container'));

      // Initialize event manager and trigger initial update
      this.eventManager.initialize();
      if (this.map && this.map.getBounds()) {
        this.eventManager.forceUpdate();
      }

      // Handle initial geolocation after map is initialized
      if (this.initialGeolocationPermission?.state === 'granted' && this.initialGeolocationPermission.enableNearMe) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.processGeolocation(position);
            this.toggleNearMeButton(true);
          },
          (error) => {
            this.displayGeolocationError(error);
            this.loadDefaultUSALocations();
          }
        );
      } else {
        this.loadDefaultUSALocations();
      }
    } catch (error) {
      console.error('Error initializing map location:', error);
    } finally {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
    }
  }

  /**
   * Initializes the Google Map instance within the given container.
   * Configures various map settings such as zoom, controls, and map ID.
   * Otherwise, it filters and loads locations based on the selected coordinates.
   */
  async initializeMap(container) {
    this.mapOptions = {
      center: this.defaultLocationCoordinates, // Map center coordinates
      zoom: this.zoomLevel, // Initial zoom level
      zoomControl: true, // Show zoom controls
      draggable: true, // Enable map dragging
      mapTypeControl: false, // Hide map type switch
      streetViewControl: false, // Hide Street View
      fullscreenControl: false, // Hide fullscreen button
      minZoom: this.zoomLevel, // Minimum zoom level
      maxZoom: this.maxZoomLevel, // Maximum zoom level
      mapId: this.googleMapId, // Custom map style ID
      disableDefaultUI: true, // Disable all default UI controls
    };

    this.map = new google.maps.Map(container, this.mapOptions);
  }

  /**
   * Handles map change events (bounds change, zoom, pan).
   * Delegates to event manager for proper throttling and processing.
   * @param {google.maps.LatLngBounds} bounds - The current map bounds
   * @param {boolean} forceUpdate - Force update even on mobile (used by Apply Now button)
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    // Delegate to event manager for proper throttling
    if (forceUpdate) {
      this.eventManager.forceUpdate(bounds);
    } else {
      this.eventManager.handleMapChange(forceUpdate);
    }
  }

  // Filters locations within the current map bounds
  filterLocationsWithinBounds(bounds) {
    if (!bounds) return [];

    const filtered = this.allLocations.filter((location) => {
      const locationLatLng = new google.maps.LatLng(parseFloat(location.latitude), parseFloat(location.longitude));
      return bounds.contains(locationLatLng);
    });

    return filtered;
  }

  /**
   * Binds UI events based on dataset attributes.
   * - If "enableNearMe" is set to true, binds the "near me" button event.
   * - If "enableSearchInput" is set to true, binds the search input event.
   * - If "enableRadiusFilter" is set to true, binds the radius filter event.
   * - If "enableBusinessTypeSearch" is set to true, binds the business type search event.
   */
  bindUIEvents() {
    // Check if all filters are enabled and bind their events
    if (
      ['enableRadiusFilter', 'enableBusinessTypeFilter', 'enableSearchInput', 'enableNearMe'].every(
        (key) => this.dataset[key] === 'true'
      )
    ) {
      this.filterManager.bindFilterEvents();
    }
  }

  /**
   * Updates the URL query parameters based on the provided filter values.
   *
   * This function modifies the browser's URL without reloading the page, ensuring that
   * search and filter parameters are reflected in the URL. If a parameter is provided,
   * it is added or updated in the query string; if it is null or undefined, it is removed.
   *
   * @param {boolean} nearByMe - Indicates if the "Near Me" filter is applied.
   * @param {string} search - The search term entered by the user.
   * @param {number} lat - latitude value for location-based filtering.
   * @param {number} lng - longitude value for location-based filtering.
   * @param {number} radius - Search radius for filtering results.
   * @param {string} businessType - The type of business being searched for.
   * @param {boolean} IsApplyFilters - Whether filters should be applied on mobile devices.
   */
  updateURLQueryParams(nearByMe, search, lat, lng, radius, businessType, IsApplyFilters) {
    if (this.isMobile && !IsApplyFilters) return;

    const params = new URLSearchParams(window.location.search);

    if (nearByMe != null) {
      params.set(this.nearByMeParams, nearByMe);
    } else {
      params.delete(this.nearByMeParams);
    }

    if (search) {
      params.set(this.searchParams, search);
    } else {
      params.delete(this.searchParams);
    }

    if (lat) {
      params.set(this.latParams, lat);
    } else {
      params.delete(this.latParams);
    }

    if (lng) {
      params.set(this.lngParams, lng);
    } else {
      params.delete(this.lngParams);
    }

    if (radius) {
      params.set(this.radiusParams, radius);
    } else {
      params.delete(this.radiusParams);
    }

    if (businessType) {
      params.set(this.businessTypeParams, businessType);
    } else {
      params.delete(this.businessTypeParams);
    }

    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);

    this.updateUIElement();
  }

  /**
   * Updates the UI elements to reflect the currently selected filters.
   *
   * This method modifies UI components based on the applied search, location,
   * business type, and radius filters. It ensures that the displayed values match
   * the selected parameters and visually updates the elements accordingly.
   */
  updateUIElement() {
    this.selectedFilterCount = 0;
    const searchElement = this.querySelector('.search-input');
    const nearMeButton = this.querySelector('.near-me-button');

    if (searchElement) {
      searchElement.value = this.selectedInputSearch;
    }

    if (this.selectedInputSearch) {
      nearMeButton.classList.remove('selected');
      this.selectedFilterCount++;
    } else if (this.selectedNearByMe) {
      nearMeButton.classList.add('selected');
      this.selectedFilterCount++;
    } else {
      nearMeButton.classList.remove('selected');
    }

    const businessTypeElement = document.getElementById('businessTypeSelectorSelectedLabel');
    if (this.selectedBusinessType) {
      businessTypeElement.innerText = this.selectedBusinessType;

      this.highlightSelectedOption(this.selectedBusinessType);

      businessTypeElement.classList.replace('text-gray-5', 'text-gray-8');
      this.selectedFilterCount++;
    } else {
      businessTypeElement.innerText = this.locationString.allFacilityType;
      businessTypeElement.classList.replace('text-gray-8', 'text-gray-5');
    }

    const radiusElement = document.getElementById('radiusSelectorSelectedLabel');
    if (this.selectedRadius > 0) {
      radiusElement.innerText = `${this.selectedRadius} miles`;

      this.highlightSelectedOption(this.selectedRadius);

      radiusElement.classList.replace('text-gray-5', 'text-gray-8');
      this.selectedFilterCount++;
    } else {
      radiusElement.innerText = this.locationString.searchRadius;
      radiusElement.classList.replace('text-gray-8', 'text-gray-5');
    }
  }

  /**
   * Highlights the selected option in the UI.
   * @param {string | number} targetAttributeValue - The value of the `data-value` attribute to match against a list item.
   */
  highlightSelectedOption(targetAttributeValue) {
    const targetedListItem = document.querySelector(`[data-value="${targetAttributeValue}"]`);
    targetedListItem.classList.add('selected-option');

    return targetedListItem;
  }

  /**
   * Binds an event listener to the "Apply Filters" button to update filters and refresh the UI.
   * On mobile, this is when filters are actually applied to the map.
   * On desktop, this provides a way to manually apply filters if needed.
   */
  bindApplyAllFiltersEvent() {
    const applyFiltersButton = document.getElementById('applyAllFilters');
    if (!applyFiltersButton) return; // Skip if button doesn't exist (e.g., on home page)

    applyFiltersButton.addEventListener('click', () => {
      const dismissModal = document.querySelector('[data-dismiss="modal-selector"]');

      // Show loader while applying filters
      if (this.isMobile) this.mapLoader.showLoader();

      // Update URL search query parameters
      if (this.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType,
          true
        );
      }

      // Apply map zoom changes for mobile (delayed from filter selection, Near Me, or search)
      if (this.isMobile) {
        // Handle Near Me location centering
        if (this.selectedNearByMe && this.selectedLocationCoordinates) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.markerCenterMapZoomLevel);
        }
        // Handle search input location centering
        else if (this.selectedInputSearch && this.selectedLocationCoordinates) {
          const zoomLevel = this.isCenterChangeRestricted ? 12 : this.zoomLevel;
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, zoomLevel);
        }
        // Handle radius-based centering
        else if (this.selectedRadius === this.defaultRadius) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
        } else if (this.map.getZoom() < this.markerCenterMapZoomLevel) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel + 2);
        }
      }

      // Apply filters to map (force update on mobile)
      if (this.mapBound) this.handleMapChangeEvent(this.mapBound, true);

      this.updateFilterCount(this.selectedFilterCount);

      // Hide loader after a short delay
      if (this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);

      if (dismissModal) {
        dismissModal.click();
      }
    });
  }

  /**
   * Updates the UI to reflect the current number of active filters.
   *
   * This method modifies the filter icon and displays the active filter count
   * when filters are applied. If no filters are active, the count is hidden.
   */
  updateFilterCount(numParams) {
    const activeFilterCountButton = document.querySelector('.active-filter-block');
    const filterIconButton = document.getElementById('filterIcon');

    if (numParams > 0) {
      filterIconButton.classList.add('selected');
      filterIconButton.classList.replace('justify-center', 'justify-between');
    }

    if (activeFilterCountButton) {
      const filterCount = activeFilterCountButton.querySelector('.filter-count');
      filterCount.textContent = numParams;
      activeFilterCountButton.style.display = numParams > 0 ? 'inline-block' : 'none';
    }
  }

  /**
   * Parses URL search parameters and updates the filter state accordingly.
   *
   * This method extracts query parameters from the URL and updates the filter values
   * such as location, search input, radius, and business type. It also ensures
   * that filters are applied correctly and updates the UI.
   */
  parseSearchParams() {
    const params = new URLSearchParams(window.location.search);

    this.selectedNearByMe = params.get(this.nearByMeParams) === 'true' || null;

    this.selectedInputSearch = params.get(this.searchParams);

    const latitude = params.get(this.latParams);
    const longitude = params.get(this.lngParams);
    if (latitude && longitude) {
      this.selectedLocationCoordinates = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
      this.locakedCenterPoint = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
    } else {
      // Fallback to default coordinates if not provided
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    }

    const radiusString = params.get(this.radiusParams);
    if (radiusString) {
      this.selectedRadius = parseInt(radiusString);
    } else {
      this.selectedRadius = this.defaultRadius; // Fallback to default radius if not provided
    }

    this.selectedBusinessType = params.get(this.businessTypeParams);

    if (this.isEnabledQueryParams) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType,
        true
      );
    }

    // Update filters count
    this.updateFilterCount(this.selectedFilterCount);
  }

  /**
   * Loads and displays location markers on the map.
   * @param {Array} locations - Array of location objects to be displayed as markers on the map.
   */
  loadLocations(locations) {
    // Delegate to page-specific logic
    this.pageLogic.loadLocations(locations);
  }

  // Gets the coordinates of the currently selected marker from the map.
  getSelectedMarkerPosition() {
    const selectedMarker = this.mapMarkers.find((marker) => marker.isSelected);
    return selectedMarker ? { lat: selectedMarker.position.lat, lng: selectedMarker.position.lng } : null;
  }

  /**
   * Handles location click event.
   * - Highlights the corresponding marker on the map.
   * - Updates the sidebar selection.
   *
   * @param {Object} location - The location object containing latitude and longitude.
   * @param {HTMLElement} locationElement - The DOM element associated with the location.
   */
  handleLocationClick(location, locationElement) {
    if (locationElement.classList.contains('selected') || locationElement.classList.contains('selected-secondary')) {
      this.deselectLocation();
      return;
    }

    // Find and highlight the corresponding marker
    const marker = this.mapMarkers.find(
      (m) => m.position.lat === parseFloat(location.latitude) && m.position.lng === parseFloat(location.longitude)
    );

    if (marker) this.handleMarkerClick(marker, location, true);
  }

  /**
   * Opens Google Maps with directions from the user's current location to the destination.
   *
   * @param {string} destination - The address or coordinates of the destination.
   */
  getDirections(destination) {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function (position) {
          const currentLocation = position.coords.latitude + ',' + position.coords.longitude;
          const mapsUrl = `https://www.google.com/maps/dir/${currentLocation}/${destination}`;
          window.open(mapsUrl, '_blank');
        },
        function () {
          showToastMessage('Error getting current location. Please try again.');
        }
      );
    } else {
      showToastMessage('Geolocation is not supported by your browser.');
    }
  }

  /**
   * Handles the click event for the schedule scan button.
   * Opens the schedule scan modal and sets up event listeners for closing it.
   *
   * @param {HTMLElement} scheduleScanButton - The button element that was clicked.
   */
  handleScheduleScanButtonClick(scheduleScanButton) {
    const userId = scheduleScanButton.getAttribute('data-target-userId');
    const modalOverlay = document.querySelector('.schedule-scan-modal-overlay');
    const modalContent = modalOverlay.querySelector('.schedule-scan-modal');
    const closeModal = document.querySelectorAll('.schedule-scan-modal-close');

    const successModal = document.getElementById('scheduleScanSuccessMessage');
    const formBlock = document.querySelector('.schedule-scan-form-block');
    const submitButton = document.getElementById('submitButton');
    document.querySelector('#scheduleScanForm input[name="userId"]').value = userId;

    modalOverlay.classList.add('active');
    modalContent.classList.add('active');
    document.body.classList.add('modal-open');

    closeModal.forEach((closeButton) => {
      closeButton.addEventListener('click', function () {
        modalOverlay.classList.remove('active');
        modalContent.classList.remove('active');
        formBlock.classList.remove('hidden');
        successModal.classList.add('hidden');
        document.body.classList.remove('modal-open');
        submitButton.disabled = true;
        scheduleScanForm.reset();
      });
    });
  }

  /**
   * Removes all existing markers from the map and clears the marker storage.
   * This helps in refreshing the markers when updating map data.
   * Use this only when you need to force clear all markers (e.g., reset filters).
   */
  clearMarkers() {
    if (this.markerClusterer) {
      this.markerClusterer?.clearMarkers();
      this.markerClusterer = null;
    }

    this.mapMarkers.forEach((marker) => marker.setMap(null));
    this.mapMarkers = [];
  }

  /**
   * Optimized marker clearing that only removes markers outside current bounds.
   * This is used by the optimized placeMarkers method.
   */
  clearMarkersOutsideBounds(bounds) {
    if (!bounds) return;

    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const withinBounds = bounds.contains(marker.position);
      if (!withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });
  }

  /**
   * Checks if the filtered locations have changed compared to the previous set.
   * Returns true if locations have changed, false if they're the same.
   */
  locationsHaveChanged(newLocations) {
    // If no previous locations, definitely changed
    if (!this.previousFilteredLocations) {
      return true;
    }

    // If different number of locations, definitely changed
    if (this.previousFilteredLocations.length !== newLocations.length) {
      return true;
    }

    // Check if all location IDs match
    const previousLocationIds = new Set(this.previousFilteredLocations.map((loc) => loc.userId));
    const newLocationIds = new Set(newLocations.map((loc) => loc.userId));

    // If sets are different sizes or have different values, locations changed
    if (previousLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (let id of newLocationIds) {
      if (!previousLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Checks if there are any active filters applied.
   * Returns true if any filters are active, false if all are at default values.
   */
  hasActiveFilters() {
    return (
      this.selectedNearByMe !== null ||
      this.selectedInputSearch !== null ||
      this.selectedRadius !== this.defaultRadius ||
      this.selectedBusinessType !== this.defaultBusinessType
    );
  }

  /**
   * Detects if Apply Now button exists and sets filtering behavior accordingly.
   * Called early in component lifecycle to ensure proper behavior during initial load.
   */
  detectFilteringBehavior() {
    // Check if "Apply Now" button exists to determine if we should use delayed mobile filtering
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;
  }

  /**
   * Checks if we should use delayed mobile filtering.
   * Returns false if detection hasn't run yet (safe default for initial load).
   */
  shouldUseDelayedFiltering() {
    // If detection hasn't run yet, default to false (instant filtering)
    if (this.shouldUseDelayedMobileFiltering === null) {
      return false;
    }
    return this.shouldUseDelayedMobileFiltering;
  }

  /**
   * Resets all mobile filter UI elements to their default state.
   */
  resetMobileFilterUI() {
    // Reset dropdown selections
    const radiusOptions = this.querySelectorAll('.select-radius');
    radiusOptions.forEach((option) => {
      option.classList.remove('selected');
      if (option.dataset.value === this.defaultRadius.toString()) {
        option.classList.add('selected');
      }
    });

    const businessTypeOptions = this.querySelectorAll('.business-type-item');
    businessTypeOptions.forEach((option) => {
      option.classList.remove('selected');
      if (option.dataset.value === this.defaultBusinessType || option.dataset.value === 'all_facilities') {
        option.classList.add('selected');
      }
    });

    // Reset Near Me button
    this.toggleNearMeButton(false);

    // Reset search input
    const searchInput = this.querySelector('.search-input');
    if (searchInput) {
      searchInput.value = '';
    }
    this.hideAutocompleteSuggestions();
  }

  /**
   * Handles the click event for a map marker.
   * Highlights the corresponding sidebar item and the marker.
   **/
  handleMarkerClick(marker, location, isSidebarClicked) {
    const showInfoWindow = () => {
      this.deselectLocation();

      if (!this.infoWindow) {
        this.infoWindow = new google.maps.InfoWindow();
        this.infoWindow.addListener('closeclick', () => {
          this.deselectLocation();
        });
      }

      // Open the infowinw
      this.openInfoWindow(marker, location);

      const sidebarItem = this.locationSidebar.findSidebarItem(location);
      const sidebarListContainer = sidebarItem?.closest('.sidebar-list-container');

      if (sidebarItem) {
        this.locationSidebar.updateSidebarSelection(location, sidebarItem);

        this.filteredLocations = [
          ...this.filteredLocations.filter((loc) => loc.userId === location.userId),
          ...this.filteredLocations.filter((loc) => loc.userId !== location.userId),
        ];

        if (sidebarListContainer) {
          sidebarListContainer.scrollTo({
            top: sidebarItem.offsetTop - sidebarListContainer.offsetTop,
            behavior: isSidebarClicked ? 'smooth' : 'auto',
          });
        }
      }
    };

    const currentZoom = this.map.getZoom();
    const needsZoom = isSidebarClicked && currentZoom < 13;

    if (needsZoom) {
      google.maps.event.addListenerOnce(this.map, 'idle', () => {
        showInfoWindow();
      });

      this.centerMapUpdateZoomLevel({ lat: location.latitude, lng: location.longitude }, 14);
    } else {
      showInfoWindow();
    }
  }

  openInfoWindow(marker, location, options = {}) {
    // Create and set InfoWindow content
    const infoContent = `<div class="info-window-wrapepr"><h2 class="text-sm font-semibold text-secondary">${location.companyName}</h2></div>`;
    this.infoWindow.setContent(infoContent);

    // Default options, with support for override
    const infoWindowOptions = {
      pixelOffset: new google.maps.Size(0, -12),
      disableAutoPan: options.disableAutoPan ?? false,
    };

    this.infoWindow.setOptions(infoWindowOptions);

    // Open InfoWindow on the specified marker
    this.infoWindow.open({
      map: this.map,
      position: marker.position,
      anchor: marker,
    });

    marker.isSelected = true; // Mark this marker as selected
    marker.zIndex = 2; // Show the selected marekr at top
    marker.content?.classList.add('selected-marker'); // Add custom styling or state class
  }

  /**
   * Sorts locations based on their distance from the selected coordinates.
   * Health Pass Partner locations are prioritized and sorted separately from non-partner locations.
   *
   * @param {Array} locations - The list of locations to be sorted.
   * @returns {Array} - A sorted array with Health Pass Partner locations first, followed by non-partner locations.
   */
  sortLocations(locations) {
    // Separate locations into Health Pass Partners and non-partners
    const healthPassLocations = locations.filter((location) => location.isHealthPassPartner);
    const nonHealthPassLocations = locations.filter((location) => !location.isHealthPassPartner);

    // Sorting function to arrange locations by distance from the selected coordinates
    const sortByDistance = (locations) =>
      locations.sort((a, b) => {
        const distanceToA = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(a.latitude),
          parseFloat(a.longitude)
        );

        const distanceToB = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(b.latitude),
          parseFloat(b.longitude)
        );

        return distanceToA - distanceToB; // Sort in ascending order (nearest first)
      });

    // Return sorted locations with Health Pass Partners first
    return [...sortByDistance(healthPassLocations), ...sortByDistance(nonHealthPassLocations)];
  }

  /**
   * Filters a list of locations based on their distance from a given point and optional business type.
   *
   * @param {Array} locations - An array of location objects. Each object should have `latitude` and `longitude` properties.
   * @param {number} lat - The latitude of the reference point.
   * @param {number} lng - The longitude of the reference point.
   * @param {number} radius - The search radius in miles.
   */
  filterLocations(locations, lat, lng, radius, businessType) {
    radius = radius ? radius * 1609.34 : Infinity;

    let filteredLocations = locations.filter((location) => {
      try {
        const distance = this.calculateDistance(
          lat,
          lng,
          parseFloat(location.latitude),
          parseFloat(location.longitude)
        );

        return distance <= radius;
      } catch (error) {
        return false;
      }
    });

    if (businessType) {
      filteredLocations = filteredLocations.filter(
        (location) => location.businessTypeName?.toLowerCase() === businessType.toLowerCase()
      );
    }

    return filteredLocations;
  }

  /**
   * Calculates the distance between two geographical points using the Google Maps API.
   *
   * @param {number} latitude1 - The latitude of the first point.
   * @param {number} longitude1 - The longitude of the first point.
   * @param {number} latitude2 - The latitude of the second point.
   * @param {number} longitude2 - The longitude of the second point.
   * @returns {number} The distance between the two points in meters.
   */
  calculateDistance(latitude1, longitude1, latitude2, longitude2) {
    return google.maps.geometry.spherical.computeDistanceBetween(
      new google.maps.LatLng(latitude1, longitude1),
      new google.maps.LatLng(latitude2, longitude2)
    );
  }

  /**
   * Centers the map on a given position and updates the zoom level.
   * @param {Object} position - The latitude and longitude coordinates to center the map on.
   * @param {number} zoomLevel - The zoom level to set for the map.
   */
  centerMapUpdateZoomLevel(position, zoomLevel) {
    if (!this.map) return;

    this.map.setCenter(position);
    this.map.setZoom(zoomLevel);
  }

  /**
   * Handles errors that occur during the geolocation retrieval process.
   * Displays an appropriate toast message based on the error type.
   * @param {Object} error - The error object returned by the geolocation API.
   */
  displayGeolocationError(error) {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        showToastMessage(this.geolocationErrorMessage.permissionDenied);
        break;
      case error.POSITION_UNAVAILABLE:
        showToastMessage(this.geolocationErrorMessage.positionUnavailable);
        break;
      case error.TIMEOUT:
        showToastMessage(this.geolocationErrorMessage.timeoutError);
        break;
      case error.UNKNOWN_ERROR:
        showToastMessage(this.geolocationErrorMessage.unknownError);
        break;
    }
  }

  /**
   * Handles the "Near Me" button click event to retrieve the user's current geolocation.
   * If geolocation is supported, it fetches the user's position and processes it.
   */
  handleNearMeClick() {
    if (!navigator.geolocation) {
      showToastMessage('Geolocation is not supported by your browser.');
      return;
    }

    if (!this.isMobile) this.mapLoader.showLoader();

    // Deselect the 'Near Me' button
    if (this.selectedNearByMe) {
      this.selectedNearByMe = null;
      this.isCenterChangeRestricted = false;
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.selectedRadius = this.defaultRadius;
      this.isCenterChangeRestricted = false;

      if (this.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType
        );
      }

      // Reset the zoom level and center the map to the default coordinates (USA)
      this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
      this.toggleNearMeButton(false); // Toggle the 'Near Me' button to select or deselect it by updating its class

      this.querySelectorAll('.select-radius').forEach((element) => {
        element.classList.remove('selected-option');
      });

      if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);

      return;
    }

    // Get the user's current geolocation and process it.
    navigator.geolocation.getCurrentPosition(
      (position) => {
        this.processGeolocation(position); // Process the position
        this.toggleNearMeButton(true); // Add the selected class

        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);
      },
      (error) => {
        this.displayGeolocationError(error); // Handle the error
        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);
      }
    );
  }

  /**
   * Processes the user's current geolocation position and filters locations based on proximity.
   * On mobile, stores the location for later application via "Apply Now" button.
   * On desktop, immediately updates the map.
   * @param {Object} position - The geolocation position object returned by the browser.
   */
  processGeolocation(position) {
    this.selectedNearByMe = true;
    this.isCenterChangeRestricted = true;
    this.selectedInputSearch = null;

    // Delegate to event manager for proper handling
    this.eventManager.handleGeolocationUpdate(position);
  }

  /**
   * Searches internal locations by company name or address
   * @param {string} query - The search query
   * @returns {Array} - Array of matching locations
   */
  searchInternalLocations(query) {
    if (!query || !this.allLocations.length) return [];

    const searchTerm = query.toLowerCase().trim();

    return this.allLocations.filter((location) => {
      const companyName = (location.companyName || '').toLowerCase();
      const address = (location.address || '').toLowerCase();

      return companyName.includes(searchTerm) || address.includes(searchTerm);
    });
  }

  /**
   * Handles the search input for location queries and updates the map with filtered locations.
   * @param {string} query - The search query entered by the user.
   */
  async handleInputSearch(query) {
    // If the query is empty, reset to default location and reload locations.
    if (query == '') {
      if (!this.isMobile) this.mapLoader.showLoader();

      this.toggleNearMeButton(false);
      this.hideAutocompleteSuggestions();

      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.isCenterChangeRestricted = false;
      this.selectedNearByMe = null;
      this.selectedInputSearch = null;
      this.selectedRadius = this.defaultRadius;

      // Update URL params and map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
      if (!this.shouldUseDelayedFiltering()) {
        if (this.isEnabledQueryParams) {
          this.updateURLQueryParams(
            this.selectedNearByMe,
            this.selectedInputSearch,
            this.selectedLocationCoordinates.lat,
            this.selectedLocationCoordinates.lng,
            this.selectedRadius,
            this.selectedBusinessType
          );
        }

        // Reset the zoom level and center the map to the default coordinates (USA)
        this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);

        setTimeout(() => this.mapLoader.hideLoader(), 500);
      }

      return;
    }

    // Search internal locations
    const internalMatches = this.searchInternalLocations(query);

    // Create internal locations suggestions
    const internalSuggestions = internalMatches.map((location) => ({
      text: `${location.companyName} - ${location.address}`,
      isInternal: true,
      location: location,
      magicKey: null,
    }));

    let suggestionList;
    if (internalSuggestions.length > 0) {
      suggestionList = internalSuggestions;
    } else {
      // Fetch Google Places results
      const googleResponse = await LocationApiService.geocodeSearch(query);

      // Create Google suggestions
      suggestionList = googleResponse.suggestions.map((suggestion) => ({
        ...suggestion,
        isInternal: false,
      }));
    }

    // Update the autocomplete suggestions with combined results
    this.updateAutocompleteSuggestions(suggestionList);
  }

  /**
   * Updates the autocomplete suggestions displayed in the UI based on the provided suggestions.
   * Clears existing suggestions and generates a new list based on the search results.
   * @param {Array} suggestions - The list of suggested locations or search results to display.
   */
  updateAutocompleteSuggestions(suggestions) {
    const autocompleteSuggestions = this.querySelector('#autocompleteSuggestions');
    autocompleteSuggestions.innerHTML = '';

    // If there are suggestions, create and display the suggestion list.
    if (suggestions.length > 0) {
      const suggestionList = document.createElement('ul');
      suggestionList.classList.add(
        'max-h-[224px]',
        'overflow-y-auto',
        'custom-scrollbar',
        'flex',
        'flex-col',
        'gap-1.5'
      );

      suggestions.forEach((data) => {
        const suggestionItem = this.createSuggestionItem(data);
        suggestionList.appendChild(suggestionItem);
      });

      autocompleteSuggestions.appendChild(suggestionList);
      autocompleteSuggestions.style.display = 'block';
    } else {
      this.defaultMessage(autocompleteSuggestions);
    }
  }

  /**
   * Displays a default message indicating that no suggestions are available.
   * @param {HTMLElement} autocompleteSuggestions - The container element where suggestions are displayed.
   */
  defaultMessage(autocompleteSuggestions) {
    const message = document.createElement('p');
    message.classList.add('text-gray-8', 'text-sm');
    message.textContent = 'No suggestions available.';

    autocompleteSuggestions.appendChild(message);
    autocompleteSuggestions.style.display = 'block';
  }

  /**
   * Creates a suggestion item for the autocomplete list based on the provided data.
   * This item is clickable and, when clicked, updates the location on the map based on the selected suggestion.
   * @param {Object} data - The suggestion data, containing text and either a magic key for geolocation or internal location data.
   * @returns {HTMLElement} suggestionItem - The created list item element representing the suggestion.
   */
  createSuggestionItem(data) {
    const suggestionItem = document.createElement('li');
    suggestionItem.classList.add('text-gray-8', 'link-item', 'text-base', 'cursor-pointer', 'select-none', 'py-1');

    suggestionItem.textContent = data.text;

    suggestionItem.addEventListener('click', async () => {
      const inputFieldElem = this.querySelector('.search-input');

      // Show loader and update UI immediately
      if (!this.isMobile) this.mapLoader.showLoader();

      inputFieldElem.value = data.text;
      this.hideAutocompleteSuggestions();
      this.toggleNearMeButton(false);

      try {
        if (data.isInternal) {
          // Handle internal location selection
          this.selectedNearByMe = null;
          this.selectedInputSearch = data.text;
          this.isCenterChangeRestricted = true;

          if (this.selectedBusinessType !== data.location.businessTypeName) {
            this.selectedBusinessType = this.defaultBusinessType;
          }

          this.selectedLocationCoordinates = {
            lat: parseFloat(data.location.latitude),
            lng: parseFloat(data.location.longitude),
          };

          // Update map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
          if (!this.shouldUseDelayedFiltering()) {
            this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, 12);
          }
        } else {
          const latLong = await LocationApiService.geocodeSearchForLatLong(data.magicKey);

          this.selectedNearByMe = null;
          this.selectedInputSearch = data.text;
          this.isCenterChangeRestricted = true;

          this.selectedLocationCoordinates = {
            lat: latLong.y,
            lng: latLong.x,
          };

          this.locakedCenterPoint = {
            lat: latLong.y,
            lng: latLong.x,
          };

          // Update map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
          if (!this.shouldUseDelayedFiltering()) {
            this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
          }
        }

        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 300);
      } catch (error) {
        console.error('Error processing suggestion:', error);
        if (!this.isMobile) this.mapLoader.hideLoader();
      }
    });

    return suggestionItem;
  }

  /**
   * Hides the autocomplete suggestions dropdown by setting its display to 'none'.
   * This method is typically called when the user clicks outside the suggestions or selects an item.
   */
  hideAutocompleteSuggestions() {
    this.querySelector('#autocompleteSuggestions').style.display = 'none';
  }

  /**
   * Toggles the "Near Me" button's selected state based on the provided boolean value.
   * If the button is enabled, it adds or removes the 'selected' class accordingly.
   * @param {boolean} isSelected - Indicates whether the button should be in the selected state.
   */
  toggleNearMeButton(isSelected) {
    if (this.dataset.enableNearMe == 'true') {
      this.nearMeButtonElem.classList.toggle('selected', isSelected);
    }
  }

  /**
   * Resets all applied filters to their default values.
   *
   * This method clears all selected filters, updates the UI, and resets the URL state.
   * It also ensures that the filter count and visual elements reflect the reset state.
   */
  resetAllFilters() {
    const resetButton = document.getElementById('resetAllFilters');
    if (!resetButton) return; // Skip if button doesn't exist (e.g., on home page)

    resetButton.addEventListener('click', () => {
      const filterIconButton = document.getElementById('filterIcon');
      const dismissModal = document.querySelector('[data-dismiss="modal-selector"]');

      // Show loader while resetting
      this.mapLoader.showLoader();

      // Reset all filter values to default
      this.selectedNearByMe = null;
      this.selectedBusinessType = null;
      this.selectedRadius = this.defaultRadius;
      this.selectedInputSearch = null;
      this.selectedFilterCount = 0;
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.isCenterChangeRestricted = false;

      // Reset all mobile filter UI elements
      this.resetMobileFilterUI();

      // Reset UI elements
      this.updateUIElement();

      window.history.replaceState({}, '', window.location.pathname);

      this.updateFilterCount(this.selectedFilterCount);

      filterIconButton.classList.remove('selected');
      filterIconButton.classList.replace('justify-between', 'justify-center');

      // Force clear all markers for a clean reset
      this.clearMarkers();

      // Clear the previous filtered locations cache to force a refresh
      this.previousFilteredLocations = null;

      // Reset map to default location
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);

      // Reload all locations after reset (force update)
      setTimeout(() => {
        if (this.mapBound) {
          this.handleMapChangeEvent(this.mapBound, true);
        }
        this.mapLoader.hideLoader();
      }, 500);

      // Close the filter dialog
      if (dismissModal) {
        dismissModal.click();
      }
    });
  }

  /**
   * Handles the closing of the modal and updates search parameters.
   *
   * This method listens for a click event on an element with the `data-dismiss="modal-selector"`
   * attribute. When clicked, it calls `parseSearchParams()` to process and update search parameters.
   */
  handleClose() {
    const dismissButton = document.querySelector('[data-dismiss="modal-selector"]');

    if (dismissButton) {
      dismissButton.addEventListener('click', () => {
        this.parseSearchParams();
      });
    }
  }

  /**
   * Deselects the currently selected location by:
   * - Closing the info window
   * - Removing selection styles from the sidebar item
   * - Hiding additional info sections
   */
  deselectLocation() {
    // Close info window if it exists
    if (this.infoWindow) {
      this.infoWindow.close();
      this.infoWindow = null;
    }

    // Reset all sidebar items
    this.querySelectorAll('.sidebar-list').forEach((item) => {
      item.classList.remove('selected', 'selected-secondary');
      item.querySelector('.others-info-wrapper')?.classList.add('hidden');
    });

    // Reset marker selection state
    this.mapMarkers.forEach((marker) => {
      marker.isSelected = false;
      marker.zIndex = 0;
      marker.content.classList.remove('selected-marker');
    });
  }

  async checkGeolocationPermission() {
    if (!navigator.geolocation) {
      return;
    }

    try {
      const permissionStatus = await navigator.permissions.query({ name: 'geolocation' });

      // Store the permission result to handle after map initialization
      this.initialGeolocationPermission = {
        state: permissionStatus.state,
        enableNearMe: this.dataset.enableNearMe === 'true',
      };

      // Listen for permission changes
      permissionStatus.addEventListener('change', () => {
        if (permissionStatus.state === 'granted') {
          this.handleNearMeClick();
        } else {
          this.loadDefaultUSALocations();
        }
      });
    } catch (error) {
      this.loadDefaultUSALocations();
    }
  }

  loadDefaultUSALocations() {
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.isCenterChangeRestricted = false;
    this.selectedNearByMe = null;
    this.selectedRadius = this.defaultRadius;

    // Center map on default USA coordinates only if map is initialized
    if (this.map) {
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);
    }
  }
}

customElements.define('map-location', MapLocation);
