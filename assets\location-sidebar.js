/**
 * LocationSidebar class handles all sidebar-related functionality for the location map
 * including displaying locations, creating location elements, and managing sidebar interactions
 */
export class LocationSidebar {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
    this.sidebarClickHandler = null;
    this.currentLocations = [];
  }

  /**
   * Displays a list of locations in the sidebar.
   * Clears existing content, creates a wrapper for location elements, and populates it based on the available locations.
   * If no locations are found, it displays a "no locations" message. Otherwise, it sorts the locations and adds them to the sidebar.
   * @param {Array} locations - The list of locations to display.
   * @param {string} selectedLocationId - The ID of the location that should remain selected
   */
  displayLocations(locations, selectedLocationId) {
    const sidebar = document.getElementById('sidebar');

    // Store locations for use in event handlers
    this.currentLocations = locations;

    // Remove existing event listener before clearing innerHTML
    sidebar.removeEventListener('click', this.sidebarClickHandler);

    // Remove existing sidebar list element
    sidebar.innerHTML = '';

    // Retrieve the value of from the sidebar dataset and convert it to a boolean
    // TODO: Add the condition for the healthpass subsciption
    const hasPayPerScanBundle = sidebar.dataset?.hasScanBundle === 'true';

    const locationWrapper = this.createLocationWrapper();
    const gridWrapper = this.createGridWrapper();

    if (locations.length === 0) {
      // Show "no locations" message if no locations are available
      gridWrapper.appendChild(this.createNoLocationElement());
    } else {
      // Sort locations before displaying them
      const sortedLocations = this.mapLocation.sortLocations(locations);

      sortedLocations.forEach((location) => {
        const locationElement = this.createLocationElement(location, hasPayPerScanBundle);

        // Show the previus selected location first into the list
        if (selectedLocationId && location.userId.toString() === selectedLocationId) {
          this.updateSidebarSelection(location, locationElement);
        }

        gridWrapper.appendChild(locationElement);
      });
    }

    locationWrapper.appendChild(gridWrapper);
    sidebar.appendChild(locationWrapper);

    // Create a bound event handler that we can remove later
    this.sidebarClickHandler = (event) => {
      let target = event.target.closest('.sidebar-list');
      if (!target) return;

      // Handle business hours click
      if (event.target.closest('.business-hours-block')) {
        event.stopPropagation();

        const locationId = target.dataset.locationId;
        const location = this.currentLocations.find((loc) => loc.userId === Number(locationId));
        this.businessHours(location);
        document.body.style.overflowY = 'hidden';
        return;
      }

      // Handle the get-directions-link
      const phone = event.target.closest('.phone');
      const email = event.target.closest('.email');
      if (phone || email) {
        event.stopPropagation();
        return;
      }

      let getDirectionsLink = event.target.closest('.address-link');
      if (getDirectionsLink) {
        event.stopPropagation();

        this.mapLocation.getDirections(getDirectionsLink.dataset.getDirections);
        return;
      }

      // Handle the schedule modal button
      const scheduleScanButton = event.target.closest('.schedule-scan-modal-selector');
      if (scheduleScanButton) {
        event.stopPropagation();

        this.mapLocation.handleScheduleScanButtonClick(scheduleScanButton);
        return;
      }

      const purchaseScanButton = event.target.closest('.book-scan button.purchase-scan-button');
      if (purchaseScanButton) {
        event.stopPropagation();

        // Store the location ID in local storage
        setLocalStorageItem(LOCAL_STORAGE_KEY.LOCATION_ID, Number(target.dataset.locationId));
        window.location.href = purchaseScanButton.dataset.href;
        return;
      }

      // Normal location selection/deselection logic
      const locationId = target.dataset.locationId;
      const location = this.currentLocations.find((loc) => loc.userId === Number(locationId));

      this.mapLocation.handleLocationClick(location, target);
    };

    // Attach event listener to the sidebar for event delegation
    sidebar.addEventListener('click', this.sidebarClickHandler);
  }

  // Creates and returns a wrapper element for the sidebar list container
  createLocationWrapper() {
    const wrapper = document.createElement('div');
    wrapper.classList.add(
      'sidebar-list-container',
      'flex-none',
      'w-full',
      'md:max-w-[354px]',
      'md:w-[354px]',
      'md:pr-1',
      'md:max-h-[600px]',
      'md:overflow-y-auto',
      'scrollbar'
    );
    return wrapper;
  }

  // This grid layout organizes location items in a single-column format with spacing
  createGridWrapper() {
    const wrapper = document.createElement('div');
    wrapper.classList.add('grid', 'grid-cols-1', 'gap-2');
    return wrapper;
  }

  /**
   * Creates an element to display when no locations are found.
   * The element includes an image and a message indicating that no search results were found.
   * @returns {HTMLElement} - A DOM element representing the "no locations found" message.
   */
  createNoLocationElement() {
    const element = document.createElement('div');
    element.classList.add('sidebar-list', 'rounded-lg', 'border', 'border-gray-2', 'p-4');
    element.innerHTML = `
      <div class="flex flex-col gap-4 justify-center items-center">
        <div class="image-icon">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/search-not-found.png?v=1708408600" loading="lazy">
        </div>
        <div class="text-center">
          <p class="text-base font-semibold text-secondary">${this.mapLocation.locationString.noSearchResult.title}</p>
          <p class="text-sm text-gray-7">${this.mapLocation.locationString.noSearchResult.description}</p>
        </div>
      </div>`;

    return element;
  }

  /**
   * Creates a location element to be displayed in the sidebar.
   * @param {Object} location - The location data to be displayed.
   * @param {boolean} hasPayPerScanBundle - Whether the Pay-Per-Scan bundle feature is enabled.
   * @returns {HTMLElement} - A DOM element representing the location.
   */
  createLocationElement(location, hasPayPerScanBundle) {
    const element = document.createElement('div');
    element.classList.add('sidebar-list', 'rounded-lg', 'cursor-pointer', 'border', 'border-gray-2', 'p-4', 'relative');
    element.setAttribute('data-location-id', `${location.userId}`);
    element.setAttribute('data-location-title', `${location.companyName}`);

    // Populate the location element with relevant details
    element.innerHTML = `<div class="space-y-4 grid">
        <div class="text-block ${location.isHealthPassPartner ? 'w-[92%]' : 'w-full'}">
          ${this.renderHealthPassInfo(location)}
          <h2 class="text-base font-bold text-secondary mb-2">${location.companyName}</h2>
          ${this.renderLocationInfo(location)}
        </div>
        ${this.renderBusinessTypeBlock(location)}
        ${this.renderBusinessHoursBlock(location)}
        ${this.renderAdditionalInfo(location)}
        ${this.renderScanButton(location, hasPayPerScanBundle)}
        <div id="${location.userId}" class="display-map-for-all-locations hidden rounded-lg"></div>
      </div>`;

    return element;
  }

  // Retrieves the ID of the currently selected location in the sidebar list.
  getSelectedLocationId() {
    const selectedItem = this.mapLocation.querySelector('.sidebar-list.selected, .sidebar-list.selected-secondary');
    return selectedItem?.dataset.locationId || null;
  }

  // Finds and returns the location object in the list matching the given ID.
  findLocationById(locations, id) {
    return id ? locations.find((loc) => loc.userId.toString() === id) : null;
  }

  // Moves the selected location to the beginning of the locations array.
  prioritizeSelectedLocation(locations, selectedLocation) {
    if (!selectedLocation) return locations;
    return [
      selectedLocation,
      ...locations.filter((loc) => loc.userId.toString() !== selectedLocation.userId.toString()),
    ];
  }

  // Checks if the sidebar location list is enabled via data attribute.
  shouldDisplaySidebar() {
    return this.mapLocation.dataset.sidebarLocationList === 'true';
  }

  // Scrolls the sidebar so the selected location is visible to the user.
  scrollToSelectedLocation(locationId) {
    const selectedElement = this.mapLocation.querySelector(`[data-location-id="${locationId}"]`);
    const container = this.mapLocation.querySelector('.sidebar-list-container');
    if (selectedElement && container) {
      container.scrollTo({
        top: selectedElement.offsetTop - container.offsetTop,
        behavior: 'auto',
      });
    }
  }

  /**
   * Finds the corresponding sidebar item for a location
   */
  findSidebarItem(location) {
    return [...document.querySelectorAll('.sidebar-list')].find(
      (item) => item.dataset.locationId === location.userId.toString()
    );
  }

  /**
   * Updates the sidebar selection when a location is clicked.
   * - Highlights the clicked location.
   * - Shows/hides additional info like phone, email, and other details.
   *
   * @param {Object} location - The selected location object.
   * @param {HTMLElement} clickedElement - The clicked sidebar element.
   */
  updateSidebarSelection(location, clickedElement) {
    // Reset all sidebar items to remove selection styles and hide additional info
    document.querySelectorAll('.sidebar-list').forEach((item) => {
      item.classList.remove('selected', 'selected-secondary');
      item.querySelector('.others-info-wrapper')?.classList.add('hidden');
    });

    // Apply selection styles to the clicked element
    if (location.isHealthPassPartner) {
      clickedElement.classList.add('selected');
    } else {
      clickedElement.classList.add('selected-secondary');
    }

    // Show additional info for the selected location
    const othersInfoWrapper = clickedElement.querySelector('.others-info-wrapper');
    if (othersInfoWrapper) {
      othersInfoWrapper.classList.remove('hidden');
    }
  }

  /**
   * Renders the Health Pass Partner badge if the location is a partner.
   * This method returns an image and a label indicating the Health Pass status.
   */
  renderHealthPassInfo(location) {
    return location.isHealthPassPartner
      ? `<img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/health-pass.png?&width=24" loading="lazy" class="absolute rotate-y-animation w-6 h-auto right-2 top-2" /><h2 class="text-primary-gradient text-[11px] font-medium leading-[16px] uppercase">${this.mapLocation.locationString.healthPassPartner}</h2>`
      : '';
  }

  /**
   * Renders location information including address, phone, and email.
   * - The address is wrapped in a clickable div with a "Get Directions" feature.
   * - The phone and email are displayed inside a hidden block, revealed when needed.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the location info.
   */
  renderLocationInfo(location) {
    return `
      <div role="button"
        data-get-directions='${encodeURIComponent(location.address)}'
        class="address-link flex w-fit gap-1 items-start group mb-2">
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Pin.svg?v=**********" loading="lazy" width="16" height="16"/>
      <p class="text-[12px] leading-[16px] text-gray-7 group-hover:text-primary">${location.address}</p>
      </div>
      <div class="contact-info-wrapper hidden">
        <div class="phone flex gap-1 items-center mb-2">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Phone.svg?v=**********" loading="lazy" width="16" height="16"/>
          <a href="tel:${location.phoneNumber}" class="text-[12px] leading-[16px] text-gray-7 hover:text-primary">${
      location.phoneNumber
    }</a>
        </div>
        <div class="email flex gap-1 items-center">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Email.svg?v=**********" loading="lazy" width="16" height="16"/>
          <a href="mailto:${location.email}" class="text-[12px] leading-[16px] text-gray-7 hover:text-primary">${
      location.email
    }</a>
        </div>
      </div>`;
  }

  /**
   * Renders the business type block for a given location.
   * - Displays the business type as a styled badge.
   * - If the location is a Health Pass Partner, additional special offerings are displayed.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the business type block.
   */
  renderBusinessTypeBlock(location) {
    if (!location.businessTypeName) return '';
    const healthPassOfferings = location.isHealthPassPartner
      ? this.mapLocation.specialOfferingHealthPass
          .map(
            (offer) =>
              `<div class="py-[2px] px-2 rounded-full text-[12px] leading-[16px] bg-[#fff6f8] text-black chips-primary-gradient-outline">${offer}</div>`
          )
          .join('')
      : '';
    return `<div class="business-type-block">
      <p class="text-[12px] leading-[16px] text-gray-7 pb-1">${this.mapLocation.locationString.businessOfferings}</p>
      <div class="select-none flex flex-wrap gap-1">
        ${healthPassOfferings}
        <div class="py-[2px] px-2 rounded-full text-[12px] leading-[16px] bg-[#EBEBEB] text-black">
          ${location.businessTypeName}
        </div>
      </div>
    </div>`;
  }

  /**
   * Renders the business hours block for a given location.
   * - Displays an interactive section with business hours information.
   * - If business hours data is missing, returns an empty string.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the business hours block.
   */
  renderBusinessHoursBlock(location) {
    if (!location.businessHoursOfOperation) return '';
    return `
    <div role="button" class="business-hours-block flex justify-between py-4 border-y border-gray-2">
      <div class="text-block flex gap-2">
        <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Clock-new.svg?v=**********" loading="lazy" />
        <p class="text-xs text-gray-7">${this.mapLocation.locationString.businessHours}</p>
      </div>
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/chevron-right.svg?v=**********" loading="lazy" />
    </div>`;
  }

  /**
   * Renders additional information about a location.
   * - Displays pricing for members and non-members.
   * - Shows appointment and walk-in requirements.
   * - Hides the section if no relevant data is available.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the additional info block.
   */
  renderAdditionalInfo(location) {
    return `
    <div class="others-info-wrapper ${
      this.hasLocationOthersInfo(location) ? 'hidden !mt-0' : !location.businessHoursOfOperation ? 'hidden' : ''
    }">
      ${
        !location.isHealthPassPartner
          ? `
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/ATM_Dollar.svg?v=**********',
            this.mapLocation.locationString.memberScanPrice,
            this.formatValue(location.howMuchForMembers, true)
          )}
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/ATM_Dollar.svg?v=**********',
            this.mapLocation.locationString.nonMemberScanPrice,
            this.formatValue(location.howMuchForNonMembers, true)
          )}
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Calendar.svg?v=**********',
            this.mapLocation.locationString.appointmentRequired,
            this.formatValue(location.doYouRequireAppointmentToScan)
          )}`
          : ''
      }
    </div>`;
  }

  /**
   * Renders the scan button block for a given location.
   * - If the location is a Health Pass Partner:
   *   - If the user has a Pay-Per-Scan bundle, shows the "Book Scan" button.
   *   - Otherwise, displays the "Purchase Scan" button.
   * - If not a Health Pass Partner, shows the "Inquire Now" button.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the scan button block.
   */
  renderScanButton(location, hasPayPerScanBundle) {
    return `<div class="button-block book-scan order-last">
      ${
        location.isHealthPassPartner
          ? hasPayPerScanBundle
            ? `${this.scheduleScanModalButton(location.userId, this.mapLocation.locationString.buttons.bookScan, true)}`
            : `${this.purchaseScanButton()}`
          : `${this.scheduleScanModalButton(
              location.userId,
              this.mapLocation.locationString.buttons.inquireNow,
              false
            )}`
      }
    </div>`;
  }

  /**
   * Checks if the location has additional information available.
   *
   * @param {Object} location - The location object containing various details.
   * @returns {boolean} - Returns true if any of the specified details are present, otherwise false.
   */
  hasLocationOthersInfo(location) {
    return (
      location.businessHoursOfOperation !== null ||
      location.howMuchForMembers !== null ||
      location.howMuchForNonMembers !== null ||
      location.doYouRequireAppointmentToScan !== null
    );
  }

  /**
   * Creates an information block with an icon, label, and value.
   * - Displays only if `value` is provided.
   * - Uses a flex layout with a label on the left and a value on the right.
   *
   * @param {string} icon - The URL of the icon to be displayed.
   * @param {string} label - The descriptive label for the information.
   * @param {string} value - The actual data to display. If falsy, the block is not rendered.
   * @returns {string} - The HTML markup for the info block or an empty string if `value` is falsy.
   */
  createInfoBlock(icon, label, value) {
    return value
      ? `<div class="info-block flex justify-between py-4 border-b border-gray-2 cursor-default">
      <div class="text-block flex gap-2">
        <img src="${icon}" width="16" height="16"/>
        <p class="text-xs text-gray-7">${label}</p>
      </div>
      <p class="text-xs text-secondary font-bold">${value}</p>
    </div>`
      : '';
  }

  /**
   * Formats a given value based on its type.
   * - Returns an empty string if the value is null or undefined.
   * - Converts boolean values to "Yes" or "No".
   * - Formats numerical values as price if `isPrice` is true.
   *
   * @param {*} value - The value to format.
   * @param {boolean} [isPrice=false] - Whether the value should be formatted as a price.
   * @returns {string} - The formatted value.
   */
  formatValue(value, isPrice = false) {
    if (value === null || value === undefined) {
      return '';
    } else if (value === true) {
      return 'Yes';
    } else if (value === false) {
      return 'No';
    } else if (isPrice && !isNaN(value)) {
      return `$${value}`;
    } else {
      return value;
    }
  }

  /**
   * Generates a schedule scan button.
   * - The button style changes based on the `isSolid` flag.
   * - Clicking the button triggers a modal for scheduling a scan.
   *
   * @param {string} userId - The unique user ID associated with the scan.
   * @param {string} label - The button label text.
   * @param {boolean} [isSolid=false] - Determines if the button should have a solid or outlined style.
   * @returns {string} - The HTML markup for the schedule scan button.
   */
  scheduleScanModalButton(userId, label, isSolid = false) {
    return `<button role="button"
      class="${
        isSolid ? 'button-primary-gradient' : 'button-primary-gradient-outline'
      } schedule-scan-modal-selector w-full flex gap-2 justify-center text-sm"
      data-target-userId="${userId}">
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/calendar-white.svg?v=1713961713"/>
      <span class="font-bold">${label}</span>
    </button>`;
  }

  /**
   * Generates a "Purchase Scan" button.
   * - This button redirects users to the pricing page.
   * - Uses a primary gradient button style for visibility.
   *
   * @returns {string} - The HTML markup for the purchase scan button.
   */
  purchaseScanButton() {
    return `<button role="button"
      data-href="${PAGE_URLS.PRICING}" class="button-primary-gradient purchase-scan-button w-full flex gap-2 justify-center text-sm">
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/calendar-white.svg?v=1713961713"/>
      <span class="font-bold">${this.mapLocation.locationString.buttons.purchaseScan}</span>
    </button>`;
  }

  /**
   * Generates and displays a modal containing business hours and scanner business hours.
   * - Parses business hours data from the location object.
   * - Creates a modal element dynamically and appends it to the document.
   * - Provides a toggle functionality for switching between business hours and scanner business hours.
   * - Implements a close button to remove the modal from the DOM.
   *
   * @param {Object} location - The location data containing business hours information.
   */
  businessHours(location) {
    const businessHoursData = location.businessHoursOfOperation ? JSON.parse(location.businessHoursOfOperation) : [];
    const scannerBusinessHoursData = location.scannerBusinessHoursOfOperation
      ? JSON.parse(location.scannerBusinessHoursOfOperation)
      : [];

    const businessHours = 'business-hours';
    const scannerBusinessHours = 'scanner-business-hours';

    const modalHTML = `<div class="schedule-scan-modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="schedule-scan-modal bg-white rounded-lg shadow-lg max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="modal-content">
          <div class="modal-content-header">
            <div class="flex justify-between items-center p-6 border-b border-gray-2">
              <div class="text-block">
                <h3 class="text-xl font-bold text-secondary">${location.companyName}</h3>
              </div>
              <button role="button" class="close-modal schedule-scan-modal-close cursor-pointer hover:scale-125">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none"><path d="M19 5.5L12 12.5M12 12.5L5 19.5M12 12.5L19 19.5M12 12.5L5 5.5" stroke="#7E7E7E" stroke-width="1.48492"/></svg>
              </button>
            </div>
          </div>
          <div class="modal-content-body p-6">
            ${
              businessHoursData.length && scannerBusinessHoursData.length
                ? `<div class="grid grid-cols-2 gap-2 rounded-lg bg-gray-1 p-1 mb-6">
                  ${[businessHours, scannerBusinessHours]
                    .map(
                      (type) => `<div class="${type}-operation">
                        <input type="radio" name="option" id="${type}-${location.userId}" value="${type}-${
                        location.userId
                      }" class="peer hidden" ${type === businessHours ? 'checked' : ''} />
                        <label for="${type}-${
                        location.userId
                      }" class="text-sm text-gray-5 block cursor-pointer select-none font-bold rounded-md p-2 text-center peer-checked:bg-white peer-checked:text-primary">
                          ${
                            type === businessHours
                              ? this.mapLocation.locationString.businessHours
                              : this.mapLocation.locationString.scanningHours
                          }
                        </label>
                      </div>`
                    )
                    .join('')}
                </div>`
                : `<p class="text-lg text-secondary border-b font-bold border-gray-2 mb-4 pb-3">${this.mapLocation.locationString.businessHours}</p>`
            }
            <div class="timing-wrapper space-y-6">
              <div class="business-hours-container space-y-4">${this.renderBusinessHours(businessHoursData)}</div>
              <div class="scanner-business-hours-container hidden space-y-4">
              ${this.renderBusinessHours(scannerBusinessHoursData)}</div>
            </div>
          </div>
        </div>
      </div>
    </div>`;

    // Create modal container and append to body
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer);

    // Add toggle functionality
    const businessHoursRadio = modalContainer.querySelector(`#business-hours-${location.userId}`);
    const scannerBusinessHoursRadio = modalContainer.querySelector(`#scanner-business-hours-${location.userId}`);
    const businessHoursContainer = modalContainer.querySelector('.business-hours-container');
    const scannerBusinessHoursContainer = modalContainer.querySelector('.scanner-business-hours-container');

    if (businessHoursRadio) {
      businessHoursRadio.addEventListener('change', () => {
        if (businessHoursRadio.checked) {
          businessHoursContainer.classList.remove('hidden');
          scannerBusinessHoursContainer.classList.add('hidden');
        }
      });
    }

    if (scannerBusinessHoursRadio) {
      scannerBusinessHoursRadio.addEventListener('change', () => {
        if (scannerBusinessHoursRadio.checked) {
          businessHoursContainer.classList.add('hidden');
          scannerBusinessHoursContainer.classList.remove('hidden');
        }
      });
    }

    // Add close functionality
    const closeButtons = modalContainer.querySelectorAll('.schedule-scan-modal-close');
    closeButtons.forEach((button) => {
      button.addEventListener('click', () => {
        modalContainer.remove();
        document.body.style.overflowY = 'auto';
      });
    });

    // Close modal when clicking outside
    const overlay = modalContainer.querySelector('.schedule-scan-modal-overlay');
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        modalContainer.remove();
        document.body.style.overflowY = 'auto';
      }
    });
  }

  /**
   * Generates HTML for displaying business hours in a structured format.
   * - Iterates through each day's business hours data.
   * - Handles special cases like "Closed" and "Open 24 Hours".
   * - Properly formats opening and closing times.
   *
   * @param {Array} businessHoursData - Array of objects containing daily business hours.
   * @returns {string} - The generated HTML for business hours display.
   */
  renderBusinessHours(businessHoursData) {
    return businessHoursData
      .map(
        (day) => `<div class="business-hours-timing grid grid-cols-2 gap-2">
          <div class="day"><p class="paragraph-text !font-bold">${day.day}</p></div>
          <div class="timing grid gap-0.5">
            ${
              day.isClosed
                ? `<p class="paragraph-text">${this.mapLocation.locationString.closed}</p>`
                : day.isOpen24
                ? `<p class="paragraph-text">${this.mapLocation.locationString.open24}</p>`
                : Array.isArray(day.businessHours) && day.businessHours.length > 0
                ? day.businessHours
                    .map(
                      (hours) => `
                      <p class="paragraph-text">
                        ${hours.openTime ? hours.openTime + 'am' : '-'} -
                        ${hours.closeTime ? hours.closeTime + 'pm' : '-'}
                      </p>
                    `
                    )
                    .join('')
                : `<p class="paragraph-text">${this.mapLocation.locationString.notAvailable}</p>`
            }
          </div>
        </div>`
      )
      .join('');
  }
}
