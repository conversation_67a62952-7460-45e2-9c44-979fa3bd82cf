/**
 * LocationMainPage class handles main location page specific functionality
 * including sidebar display, filtering, and full location management features
 */
export class LocationMainPage {
  constructor(mapLocationInstance) {
    this.mapLocation = mapLocationInstance;
  }

  /**
   * Initializes main location page specific features
   */
  initialize() {
    this.setupFilteringFeatures();
    this.setupSidebarFeatures();
    this.setupMobileFiltering();
  }

  /**
   * Sets up filtering features specific to main location page
   */
  setupFilteringFeatures() {
    // Enable all filtering capabilities
    this.mapLocation.isEnableRadiusFilter = this.mapLocation.dataset.enableRadiusFilter === 'true';
    this.mapLocation.isEnabledQueryParams = this.mapLocation.dataset.enableUpdateQuearyParams === 'true';

    // Setup delayed mobile filtering if apply button exists
    this.mapLocation.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.mapLocation.shouldUseDelayedMobileFiltering = this.mapLocation.isMobile && this.mapLocation.hasApplyButton;
  }

  /**
   * Sets up sidebar features for main location page
   */
  setupSidebarFeatures() {
    // Enable sidebar display
    this.enableSidebar = this.mapLocation.dataset.sidebarLocationList === 'true';

    if (this.enableSidebar) {
      this.setupSidebarEventListeners();
    }
  }

  /**
   * Sets up mobile filtering specific to main location page
   */
  setupMobileFiltering() {
    if (this.mapLocation.isMobile && this.mapLocation.hasApplyButton) {
      this.setupDelayedFiltering();
    }
  }

  /**
   * Sets up delayed filtering for mobile devices
   */
  setupDelayedFiltering() {
    const applyButton = document.getElementById('applyAllFilters');
    const resetButton = document.getElementById('resetAllFilters');

    if (applyButton) {
      applyButton.addEventListener('click', () => {
        this.applyFilters();
      });
    }

    if (resetButton) {
      resetButton.addEventListener('click', () => {
        this.resetFilters();
      });
    }
  }

  /**
   * Sets up sidebar event listeners
   */
  setupSidebarEventListeners() {
    // Additional sidebar-specific event listeners can be added here
    // This is where main page specific sidebar behavior would go
  }

  /**
   * Applies filters when Apply button is clicked (mobile)
   */
  applyFilters() {
    // Force update through event manager
    this.mapLocation.eventManager.forceUpdate();

    // Hide the filter modal if it exists
    const filterModal = document.querySelector('[data-target="filterModalSelector"]');
    if (filterModal) {
      const dismissButton = filterModal.querySelector('[data-dismiss="modal-selector"]');
      if (dismissButton) {
        dismissButton.click();
      }
    }
  }

  /**
   * Resets all filters to default values
   */
  resetFilters() {
    this.mapLocation.resetAllFilters();
  }

  /**
   * Handles location loading specific to main page
   * @param {Array} locations - Array of locations to load
   */
  loadLocations(locations) {
    const selectedLocationId = this.mapLocation.locationSidebar.getSelectedLocationId();
    const selectedMarkerPosition = this.mapLocation.getSelectedMarkerPosition();
    const selectedLocation = this.mapLocation.locationSidebar.findLocationById(locations, selectedLocationId);

    // Move the selected location to the top of the list
    locations = this.mapLocation.locationSidebar.prioritizeSelectedLocation(locations, selectedLocation);

    // Update sidebar list if enabled
    if (this.enableSidebar) {
      this.mapLocation.locationSidebar.displayLocations(locations, selectedLocationId);
      this.mapLocation.locationSidebar.scrollToSelectedLocation(selectedLocationId);
    }

    const markers = this.mapLocation.markerClusterer.placeMarkers(locations);
    this.mapLocation.markerClusterer.initMarkerClusterer(markers);

    // Re-select previously selected marker/location if applicable
    this.mapLocation.markerClusterer.restoreSelectedMarker(selectedLocation, selectedMarkerPosition, locations);
  }

  /**
   * Handles map change events specific to main page
   * @param {Object} bounds - Map bounds
   * @param {boolean} forceUpdate - Whether to force update
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    // Check if we should use delayed filtering
    if (this.shouldUseDelayedFiltering() && !forceUpdate && this.mapLocation.hasActiveFilters()) {
      return; // Skip filter application on mobile location page unless forced
    }

    // Reset the radius to default if the map center is outside the locked center point
    if (this.mapLocation.locakedCenterPoint && bounds && !bounds.contains(this.mapLocation.locakedCenterPoint)) {
      this.mapLocation.selectedRadius = this.mapLocation.defaultRadius;
    }

    const filteredLocationsWithinBounds = this.mapLocation.filterLocationsWithinBounds(bounds);

    // Update the selected location coordinates only if the center is not restricted
    if (!this.mapLocation.isCenterChangeRestricted) {
      this.mapLocation.selectedLocationCoordinates = {
        lat: this.mapLocation.map.getCenter().lat(),
        lng: this.mapLocation.map.getCenter().lng(),
      };
    }

    // Update URL query parameters if enabled and if not using search input or 'Near Me' filter
    if (
      this.mapLocation.isEnabledQueryParams &&
      (!this.mapLocation.enableSearchInput || !this.mapLocation.selectedNearByMe)
    ) {
      this.mapLocation.updateURLQueryParams(
        this.mapLocation.selectedNearByMe,
        this.mapLocation.selectedInputSearch,
        this.mapLocation.selectedLocationCoordinates.lat,
        this.mapLocation.selectedLocationCoordinates.lng,
        this.mapLocation.selectedRadius,
        this.mapLocation.selectedBusinessType
      );
    }

    // Filter locations based on the selected criteria
    const newFilteredLocations = this.mapLocation.filterLocations(
      filteredLocationsWithinBounds,
      this.mapLocation.selectedLocationCoordinates.lat,
      this.mapLocation.selectedLocationCoordinates.lng,
      this.mapLocation.selectedRadius,
      this.mapLocation.selectedBusinessType
    );

    // Only load locations if they have actually changed
    if (this.mapLocation.locationsHaveChanged(newFilteredLocations)) {
      this.mapLocation.previousFilteredLocations = [...newFilteredLocations]; // Store copy for next comparison
      this.mapLocation.filteredLocations = newFilteredLocations;
      this.loadLocations(this.mapLocation.filteredLocations);
    }
  }

  /**
   * Checks if delayed filtering should be used
   * @returns {boolean}
   */
  shouldUseDelayedFiltering() {
    return this.mapLocation.shouldUseDelayedMobileFiltering;
  }

  /**
   * Gets page-specific configuration
   * @returns {Object} Configuration object
   */
  getPageConfig() {
    return {
      enableSidebar: this.enableSidebar,
      enableFiltering: true,
      enableDelayedFiltering: this.mapLocation.shouldUseDelayedMobileFiltering,
      enableQueryParams: this.mapLocation.isEnabledQueryParams,
      enableRadiusFilter: this.mapLocation.isEnableRadiusFilter,
      pageType: 'main-location',
    };
  }

  /**
   * Handles page-specific cleanup
   */
  cleanup() {
    // Remove any main page specific event listeners
    const applyButton = document.getElementById('applyAllFilters');
    const resetButton = document.getElementById('resetAllFilters');

    if (applyButton) {
      applyButton.removeEventListener('click', this.applyFilters);
    }

    if (resetButton) {
      resetButton.removeEventListener('click', this.resetFilters);
    }
  }
}
