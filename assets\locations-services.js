// A utility class for loading the Google Maps JavaScript API dynamically.
export class GoogleMapsLoader {
  /**
   * Loads the Google Maps JavaScript API with the specified configuration.
   *
   * @param {Object} config - The configuration object for loading the API.
   * @param {string} config.key - The API key for Google Maps.
   * @param {string} [config.libraries] - Comma-separated list of additional libraries to load (e.g., "places").
   * @param {boolean} [config.markerClusterer] - Whether to load the Marker Clusterer library.
   *
   * @returns {Promise<void>} - A promise that resolves when the API is successfully loaded.
   */
  static loadGoogleMapsAPI(config) {
    const { key, libraries, markerClusterer } = config;

    return new Promise((resolve, reject) => {
      // Check if the Google Maps script is already loaded
      if (window.google && window.google.maps) {
        resolve();
        return;
      }

      // Check if the script is already in the DOM
      const existingScript = document.getElementById('google-maps-script');
      if (existingScript) {
        existingScript.addEventListener('load', () => resolve());
        return;
      }

      // Create a new script element to load the API
      const script = document.createElement('script');
      script.id = 'google-maps-script';

      // Construct the API URL with the provided key and libraries
      const params = new URLSearchParams({ key, callback: 'googleMapsCallback', libraries });
      script.src = `https://maps.googleapis.com/maps/api/js?${params.toString()}&loading=async`;
      script.async = true; // Load the script asynchronously
      script.defer = true; // Defer execution until after the HTML document is parsed

      // Define the global callback function to resolve the promise when API is ready
      window.googleMapsCallback = () => {
        if (markerClusterer) {
          GoogleMapsLoader.loadMarkerClusterer().then(resolve).catch(reject);
        } else {
          resolve();
        }
      };

      script.onerror = () => reject(new Error('Failed to load the Google Maps JavaScript API'));
      document.head.appendChild(script);
    });
  }

  /**
   * Loads the Marker Clusterer library.
   *
   * @returns {Promise<void>} - A promise that resolves when the Marker Clusterer library is loaded.
   */
  static loadMarkerClusterer() {
    return new Promise((resolve, reject) => {
      if (window.MarkerClusterer) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.id = 'marker-clusterer-script';
      script.src = 'https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js';
      script.async = true;
      script.defer = true;

      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Marker Clusterer API'));

      document.head.appendChild(script);
    });
  }
}

// Service class for handling location-related API calls.
export class LocationApiService {
  /**
   * Fetches all locations from the given API URL.
   *
   * @param {string} apiUrl - The URL of the API to fetch locations.
   * @returns {Promise<Object[]>} - A promise resolving to the list of locations.
   */
  static async fetchLocations(apiUrl) {
    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error('Failed to load locations');
      }

      return await response.json(); // Return locations instead of assigning to a class property
    } catch (error) {
      console.error('Error loading locations:', error);
      return []; // Return an empty array in case of error
    }
  }

  /**
   * Performs a geocode search based on the query.
   *
   * @param {string} query - The search term for the geocode API.
   * @returns {Promise<Object>} - A promise resolving to the API response containing suggestions.
   */
  static async geocodeSearch(query) {
    try {
      const response = await fetch(
        `https://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/suggest?f=pjson&text=${encodeURIComponent(
          query
        )}`
      );

      return response.ok ? await response.json() : { suggestions: [] };
    } catch (error) {
      console.error('Error during geocode search:', error);
      return { suggestions: [] };
    }
  }

  /**
   * Retrieves latitude and longitude for a location using a magic key.
   *
   * @param {string} query - The magic key for the geocode API.
   * @returns {Promise<Object>} - A promise resolving to the location coordinates (x, y).
   */
  static async geocodeSearchForLatLong(query) {
    try {
      const response = await fetch(
        `https://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/findAddressCandidates?outFields=*&forStorage=false&f=pjson&&magicKey=${encodeURIComponent(
          query
        )}`
      );

      if (response.ok) {
        const data = await response.json();
        return data?.candidates[0]?.location || { x: 0, y: 0 };
      }

      return { x: 0, y: 0 };
    } catch (error) {
      console.error('Error during geocode search for latitude and longitude:', error);
      return { x: 0, y: 0 };
    }
  }
}

class MapLoader {
  /**
   * @param {HTMLElement} container - The DOM element that will contain the loader
   */
  constructor(container) {
    this.container = container;
    this.loaderWrapper = null;
    this.loader = null;
  }

  // Method to create the loader element dynamically
  createLoader() {
    if (!this.loaderWrapper) {
      this.loaderWrapper = document.createElement('div');
      this.loaderWrapper.classList.add('loader-wrapper');
      this.loaderWrapper.style.position = 'absolute';

      this.loader = document.createElement('div');
      this.loader.classList.add('loader');

      this.loaderWrapper.appendChild(this.loader);
      this.container.style.position = 'relative';
      this.container.appendChild(this.loaderWrapper);
    }
  }

  // Method to show the loader
  showLoader() {
    if (!this.loaderWrapper) {
      this.createLoader();
    }
    this.loaderWrapper.classList.remove('hidden');
  }

  // Method to hide the loader smoothly
  hideLoader() {
    if (this.loaderWrapper) {
      this.loaderWrapper.classList.add('hidden');

      setTimeout(() => {
        if (this.loaderWrapper) {
          this.container.removeChild(this.loaderWrapper);
          this.loaderWrapper = null;
        }
      }, 1000); // Wait for 1 second before removing the loader
    }
  }
}
